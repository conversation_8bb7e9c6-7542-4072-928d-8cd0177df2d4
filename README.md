# Java SSH Client

一个基于JavaFX开发的现代化SSH客户端，提供直观的用户界面和丰富的功能。

## 功能特性

### 🚀 核心功能
- **现代化界面设计** - 采用现代化UI设计，操作便捷，用户体验优秀
- **SSH连接管理** - 支持密码和私钥认证，可保存连接配置
- **交互式终端** - 全功能SSH终端，支持命令历史记录
- **文件管理** - 完整的远程文件管理功能，支持上传下载
- **实时监控** - 监控服务器CPU、内存、磁盘、网络等状态

### 📊 监控功能
- CPU使用率实时监控
- 内存使用情况显示
- 磁盘空间使用统计
- 网络流量监控
- 系统负载和进程数统计
- 可配置的监控间隔和警告阈值

### 💾 数据持久化
- 连接信息安全存储
- 命令历史记录
- 个性化设置保存
- 支持连接配置导入导出

### 🎨 个性化设置
- 多种界面主题
- 可调节字体大小和样式
- 终端颜色方案配置
- 连接参数自定义

## 系统要求

- **Java版本**: Java 17 或更高版本
- **JavaFX**: JavaFX 19 或更高版本
- **操作系统**: Windows 10+, macOS 10.14+, Linux (Ubuntu 18.04+)
- **内存**: 最少512MB RAM
- **磁盘空间**: 100MB可用空间

## 快速开始

### 1. 环境准备

确保已安装Java 17或更高版本：
```bash
java -version
```

### 2. 编译项目

```bash
# 克隆项目
git clone https://github.com/your-repo/java-ssh-client.git
cd java-ssh-client

# 使用Maven编译
mvn clean compile
```

### 3. 运行应用

```bash
# 使用Maven运行
mvn javafx:run

# 或者编译后运行JAR文件
mvn clean package
java -jar target/java-ssh-client-1.0.0.jar
```

### 4. 创建连接

1. 启动应用后，在"连接"标签页填写服务器信息
2. 选择认证方式（密码或私钥）
3. 可选择保存连接配置以便下次使用
4. 点击"连接"按钮建立SSH连接

## 使用指南

### 连接管理
- **新建连接**: 在连接标签页填写服务器信息
- **保存连接**: 勾选"保存此连接"可将配置保存到本地
- **快速连接**: 从下拉框选择已保存的连接
- **测试连接**: 点击"测试连接"验证配置是否正确

### 终端操作
- **命令执行**: 在命令输入框输入命令并按回车
- **历史记录**: 使用上下箭头键浏览命令历史
- **清空终端**: 点击"清空"按钮清除终端输出
- **复制粘贴**: 支持标准的Ctrl+C/Ctrl+V操作

### 文件管理
- **浏览文件**: 左侧树形结构浏览目录，右侧表格显示文件详情
- **上传文件**: 点击"上传"按钮选择本地文件上传到服务器
- **下载文件**: 选择文件后点击"下载"保存到本地
- **文件操作**: 支持删除、重命名、创建文件夹等操作

### 系统监控
- **启动监控**: 点击"开始监控"按钮开始实时监控
- **监控数据**: 查看CPU、内存、磁盘、网络等实时数据
- **自动监控**: 可设置连接时自动开始监控
- **监控设置**: 在设置中配置监控间隔和警告阈值

## 配置文件

应用程序的配置文件存储在用户主目录下的`.java-ssh-client`文件夹中：

```
~/.java-ssh-client/
├── connections.json      # 保存的连接配置
├── commands_history.txt  # 命令历史记录
├── settings.properties   # 应用程序设置
└── logs/                # 日志文件
    ├── ssh-client.log
    └── ssh-client-error.log
```

## 开发指南

### 项目结构

```
src/main/java/com/sshtool/
├── Main.java                    # 应用程序入口
├── controller/                  # 控制器类
│   ├── MainController.java
│   └── SettingsController.java
├── model/                       # 数据模型
│   ├── ConnectionInfo.java
│   ├── ServerStats.java
│   └── FileItem.java
└── service/                     # 服务类
    ├── ConnectionManager.java
    ├── FileManager.java
    ├── HistoryManager.java
    └── MonitoringService.java

src/main/resources/
├── com/sshtool/                # FXML文件
│   ├── main-view.fxml
│   └── settings-view.fxml
├── css/                        # 样式文件
│   └── modern-theme.css
└── images/                     # 图标资源
```

### 构建和打包

```bash
# 编译项目
mvn clean compile

# 运行测试
mvn test

# 打包应用
mvn clean package

# 创建可执行JAR
mvn clean package shade:shade
```

## 故障排除

### 常见问题

1. **连接失败**
   - 检查网络连接
   - 验证服务器地址和端口
   - 确认用户名和密码正确
   - 检查防火墙设置

2. **私钥认证失败**
   - 确认私钥文件路径正确
   - 检查私钥文件权限
   - 验证私钥格式（支持OpenSSH和PuTTY格式）

3. **文件传输失败**
   - 检查文件权限
   - 确认磁盘空间充足
   - 验证网络连接稳定性

4. **监控数据异常**
   - 确认服务器支持相关命令
   - 检查用户权限
   - 验证系统兼容性

### 日志查看

应用程序日志存储在`~/.java-ssh-client/logs/`目录中：
- `ssh-client.log`: 一般日志信息
- `ssh-client-error.log`: 错误日志信息

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目！

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 更新日志

### v1.0.0 (2024-09-02)
- 初始版本发布
- 基础SSH连接功能
- 文件管理功能
- 系统监控功能
- 现代化UI界面
- 配置持久化存储

## 联系方式

- 项目主页: https://github.com/your-repo/java-ssh-client
- 问题反馈: https://github.com/your-repo/java-ssh-client/issues
- 邮箱: <EMAIL>

---

**Java SSH Client** - 让SSH连接更简单、更直观！
