package com.sshtool.model;

import java.util.Date;

/**
 * 文件项类，表示文件系统中的一个文件或目录
 */
public class FileItem {
    private String name;          // 文件名
    private String path;          // 文件完整路径
    private boolean isDirectory;  // 是否为目录
    private long size;            // 文件大小（字节）
    private Date lastModified;    // 最后修改时间
    private String permissions;   // 文件权限
    private String owner;         // 文件所有者
    private String group;         // 文件组
    private boolean isHidden;     // 是否为隐藏文件
    private boolean isSymlink;    // 是否为符号链接
    private String linkTarget;    // 符号链接目标

    /**
     * 构造函数
     * @param name 文件名
     * @param path 文件路径
     * @param isDirectory 是否为目录
     * @param size 文件大小
     * @param lastModified 最后修改时间
     */
    public FileItem(String name, String path, boolean isDirectory, long size, Date lastModified) {
        this.name = name;
        this.path = path;
        this.isDirectory = isDirectory;
        this.size = size;
        this.lastModified = lastModified;
        this.permissions = "";
        this.owner = "";
        this.group = "";
        this.isHidden = name.startsWith(".");
        this.isSymlink = false;
        this.linkTarget = "";
    }

    /**
     * 完整构造函数
     */
    public FileItem(String name, String path, boolean isDirectory, long size, 
                   Date lastModified, String permissions, String owner, String group) {
        this(name, path, isDirectory, size, lastModified);
        this.permissions = permissions;
        this.owner = owner;
        this.group = group;
    }

    // Getter和Setter方法
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
        this.isHidden = name.startsWith(".");
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public boolean isDirectory() {
        return isDirectory;
    }

    public void setDirectory(boolean directory) {
        isDirectory = directory;
    }

    public long getSize() {
        return size;
    }

    public void setSize(long size) {
        this.size = size;
    }

    public Date getLastModified() {
        return lastModified;
    }

    public void setLastModified(Date lastModified) {
        this.lastModified = lastModified;
    }

    public String getPermissions() {
        return permissions;
    }

    public void setPermissions(String permissions) {
        this.permissions = permissions;
    }

    public String getOwner() {
        return owner;
    }

    public void setOwner(String owner) {
        this.owner = owner;
    }

    public String getGroup() {
        return group;
    }

    public void setGroup(String group) {
        this.group = group;
    }

    public boolean isHidden() {
        return isHidden;
    }

    public void setHidden(boolean hidden) {
        isHidden = hidden;
    }

    public boolean isSymlink() {
        return isSymlink;
    }

    public void setSymlink(boolean symlink) {
        isSymlink = symlink;
    }

    public String getLinkTarget() {
        return linkTarget;
    }

    public void setLinkTarget(String linkTarget) {
        this.linkTarget = linkTarget;
    }

    /**
     * 格式化文件大小为可读字符串
     */
    public String getFormattedSize() {
        if (isDirectory) {
            return "<DIR>";
        }
        
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.1f KB", size / 1024.0);
        } else if (size < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", size / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", size / (1024.0 * 1024.0 * 1024.0));
        }
    }

    /**
     * 获取文件扩展名
     */
    public String getExtension() {
        if (isDirectory || name == null) {
            return "";
        }
        
        int lastDot = name.lastIndexOf('.');
        if (lastDot > 0 && lastDot < name.length() - 1) {
            return name.substring(lastDot + 1).toLowerCase();
        }
        return "";
    }

    /**
     * 判断是否为可执行文件
     */
    public boolean isExecutable() {
        return permissions != null && permissions.length() >= 3 && 
               (permissions.charAt(2) == 'x' || permissions.charAt(5) == 'x' || permissions.charAt(8) == 'x');
    }

    /**
     * 判断是否可读
     */
    public boolean isReadable() {
        return permissions != null && permissions.length() >= 1 && permissions.charAt(0) == 'r';
    }

    /**
     * 判断是否可写
     */
    public boolean isWritable() {
        return permissions != null && permissions.length() >= 2 && permissions.charAt(1) == 'w';
    }

    /**
     * 获取文件类型图标名称（用于UI显示）
     */
    public String getIconName() {
        if (isDirectory) {
            return "folder";
        }
        
        String ext = getExtension();
        switch (ext) {
            case "txt":
            case "log":
            case "md":
                return "text-file";
            case "jpg":
            case "jpeg":
            case "png":
            case "gif":
            case "bmp":
                return "image-file";
            case "mp3":
            case "wav":
            case "flac":
                return "audio-file";
            case "mp4":
            case "avi":
            case "mkv":
                return "video-file";
            case "zip":
            case "rar":
            case "tar":
            case "gz":
                return "archive-file";
            case "java":
            case "py":
            case "js":
            case "html":
            case "css":
                return "code-file";
            default:
                return isExecutable() ? "executable-file" : "generic-file";
        }
    }

    @Override
    public String toString() {
        return name;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        FileItem fileItem = (FileItem) obj;
        return path != null ? path.equals(fileItem.path) : fileItem.path == null;
    }

    @Override
    public int hashCode() {
        return path != null ? path.hashCode() : 0;
    }
}
