package com.sshtool.model;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 文件项类，表示文件系统中的一个文件或目录
 */
public class FileItem {
    private String name;          // 文件名
    private String path;          // 文件完整路径
    private boolean isDirectory;  // 是否为目录
    private long size;            // 文件大小（字节）
    private Date lastModified;    // 最后修改时间

    /**
     * 构造函数
     * @param name 文件名
     * @param path 文件路径
     * @param isDirectory 是否为目录
     * @param size 文件大小
     * @param lastModified 最后修改时间
     */
    public FileItem(String name, String path, boolean isDirectory, long size, Date lastModified) {
        this.name = name;
        this.path = path;
        this.isDirectory = isDirectory;
        this.size = size;
        this.lastModified = lastModified;
    }

    /**
     * 获取文件名
     * @return 文件名
     */
    public String getName() {
        return name;
    }

    /**
     * 设置文件名
     * @param name 文件名
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 获取文件完整路径
     * @return 文件完整路径
     */
    public String getPath() {
        return path;
    }

    /**
     * 设置文件完整路径
     * @param path 文件完整路径
     */
    public void setPath(String path) {
        this.path = path;
    }

    /**
     * 判断是否为目录
     * @return 是否为目录
     */
    public boolean isDirectory() {
        return isDirectory;
    }

    /**
     * 设置是否为目录
     * @param directory 是否为目录
     */
    public void setDirectory(boolean directory) {
        isDirectory = directory;
    }

    /**
     * 获取文件大小
     * @return 文件大小（字节）
     */
    public long getSize() {
        return size;
    }

    /**
     * 设置文件大小
     * @param size 文件大小（字节）
     */
    public void setSize(long size) {
        this.size = size;
    }

    /**
     * 获取最后修改时间
     * @return 最后修改时间
     */
    public Date getLastModified() {
        return lastModified;
    }

    /**
     * 设置最后修改时间
     * @param lastModified 最后修改时间
     */
    public void setLastModified(Date lastModified) {
        this.lastModified = lastModified;
    }

    /**
     * 获取格式化后的文件大小
     * @return 格式化后的文件大小字符串
     */
    public String getFormattedSize() {
        if (isDirectory) {
            return "-";
        }

        String[] units = {"B", "KB", "MB", "GB", "TB"};
        double size = this.size;
        int unitIndex = 0;

        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }

        return String.format("%.2f %s", size, units[unitIndex]);
    }

    /**
     * 获取格式化后的最后修改时间
     * @return 格式化后的最后修改时间字符串
     */
    public String getFormattedLastModified() {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return dateFormat.format(lastModified);
    }

    /**
     * 检查文件是否存在（本地文件）
     * @return 文件是否存在
     */
    public boolean exists() {
        return new File(path).exists();
    }

    @Override
    public String toString() {
        return name;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        FileItem fileItem = (FileItem) o;

        return path != null ? path.equals(fileItem.path) : fileItem.path == null;
    }

    @Override
    public int hashCode() {
        return path != null ? path.hashCode() : 0;
    }
}