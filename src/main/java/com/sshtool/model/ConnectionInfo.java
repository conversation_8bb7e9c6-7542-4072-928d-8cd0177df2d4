package com.sshtool.model;

import java.io.Serializable;
import java.util.Objects;

/**
 * 连接信息类，存储SSH连接的相关参数
 */
public class ConnectionInfo implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private String hostname;
    private int port;
    private String username;
    private String password;
    private String privateKeyPath;
    private String passphrase;
    private String nickname;
    private boolean usePrivateKey;
    private long lastConnected;
    
    /**
     * 构造函数，初始化连接信息
     */
    public ConnectionInfo() {
        this.port = 22; // 默认SSH端口
        this.usePrivateKey = false;
        this.lastConnected = 0;
    }
    
    /**
     * 带参数的构造函数
     */
    public ConnectionInfo(String hostname, int port, String username, String password) {
        this();
        this.hostname = hostname;
        this.port = port;
        this.username = username;
        this.password = password;
    }
    
    // Getter和Setter方法
    public String getHostname() {
        return hostname;
    }
    
    public void setHostname(String hostname) {
        this.hostname = hostname;
    }
    
    public int getPort() {
        return port;
    }
    
    public void setPort(int port) {
        this.port = port;
    }
    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public String getPassword() {
        return password;
    }
    
    public void setPassword(String password) {
        this.password = password;
    }
    
    public String getPrivateKeyPath() {
        return privateKeyPath;
    }
    
    public void setPrivateKeyPath(String privateKeyPath) {
        this.privateKeyPath = privateKeyPath;
    }
    
    public String getPassphrase() {
        return passphrase;
    }
    
    public void setPassphrase(String passphrase) {
        this.passphrase = passphrase;
    }
    
    public String getNickname() {
        return nickname;
    }
    
    public void setNickname(String nickname) {
        this.nickname = nickname;
    }
    
    public boolean isUsePrivateKey() {
        return usePrivateKey;
    }
    
    public void setUsePrivateKey(boolean usePrivateKey) {
        this.usePrivateKey = usePrivateKey;
    }
    
    public long getLastConnected() {
        return lastConnected;
    }
    
    public void setLastConnected(long lastConnected) {
        this.lastConnected = lastConnected;
    }
    
    /**
     * 返回连接信息的字符串表示，用于在下拉框中显示
     */
    @Override
    public String toString() {
        if (nickname != null && !nickname.isEmpty()) {
            return nickname + " (" + username + "@" + hostname + ":" + port + ")";
        }
        return username + "@" + hostname + ":" + port;
    }
    
    /**
     * 判断两个连接信息是否相等
     */
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ConnectionInfo that = (ConnectionInfo) o;
        return port == that.port &&
               Objects.equals(hostname, that.hostname) &&
               Objects.equals(username, that.username);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(hostname, port, username);
    }
    
    /**
     * 验证连接信息是否完整
     */
    public boolean isValid() {
        if (hostname == null || hostname.trim().isEmpty()) {
            return false;
        }
        if (username == null || username.trim().isEmpty()) {
            return false;
        }
        if (port <= 0 || port > 65535) {
            return false;
        }
        if (usePrivateKey) {
            return privateKeyPath != null && !privateKeyPath.trim().isEmpty();
        } else {
            return password != null && !password.isEmpty();
        }
    }
    
    /**
     * 创建连接信息的副本
     */
    public ConnectionInfo copy() {
        ConnectionInfo copy = new ConnectionInfo();
        copy.hostname = this.hostname;
        copy.port = this.port;
        copy.username = this.username;
        copy.password = this.password;
        copy.privateKeyPath = this.privateKeyPath;
        copy.passphrase = this.passphrase;
        copy.nickname = this.nickname;
        copy.usePrivateKey = this.usePrivateKey;
        copy.lastConnected = this.lastConnected;
        return copy;
    }
}
