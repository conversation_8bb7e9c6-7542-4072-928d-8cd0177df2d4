package com.sshtool.model;

import java.io.Serializable;
import java.util.Objects;

/**
 * 连接信息类，存储SSH连接的相关参数
 */
public class ConnectionInfo implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private String hostname;
    private int port;
    private String username;
    private String password;
    private String privateKeyPath;
    private String passphrase;
    private String nickname;
    
    /**
     * 构造函数，初始化连接信息
     */
    public ConnectionInfo() {
        this.port = 22; // 默认SSH端口
    }
    
    /**
     * 构造函数，初始化连接信息
     * @param hostname 主机名或IP地址
     * @param port 端口号
     * @param username 用户名
     * @param password 密码
     * @param privateKeyPath 私钥路径
     * @param passphrase 私钥密码
     */
    public ConnectionInfo(String hostname, int port, String username, String password, String privateKeyPath, String passphrase) {
        this.hostname = hostname;
        this.port = port;
        this.username = username;
        this.password = password;
        this.privateKeyPath = privateKeyPath;
        this.passphrase = passphrase;
    }
    
    /**
     * 构造函数，初始化连接信息
     * @param hostname 主机名或IP地址
     * @param port 端口号
     * @param username 用户名
     * @param password 密码
     */
    public ConnectionInfo(String hostname, int port, String username, String password) {
        this(hostname, port, username, password, null, null);
    }
    
    // Getters and Setters
    public String getHostname() {
        return hostname;
    }
    
    public void setHostname(String hostname) {
        this.hostname = hostname;
    }
    
    public int getPort() {
        return port;
    }
    
    public void setPort(int port) {
        this.port = port;
    }
    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public String getPassword() {
        return password;
    }
    
    public void setPassword(String password) {
        this.password = password;
    }
    
    public String getPrivateKeyPath() {
        return privateKeyPath;
    }
    
    public void setPrivateKeyPath(String privateKeyPath) {
        this.privateKeyPath = privateKeyPath;
    }
    
    public String getPassphrase() {
        return passphrase;
    }
    
    public void setPassphrase(String passphrase) {
        this.passphrase = passphrase;
    }
    
    public String getNickname() {
        return nickname;
    }
    
    public void setNickname(String nickname) {
        this.nickname = nickname;
    }
    
    /**
     * 返回连接信息的字符串表示，用于在下拉框中显示
     */
    @Override
    public String toString() {
        if (nickname != null && !nickname.isEmpty()) {
            return nickname + " (" + username + "@" + hostname + ":" + port + ")";
        }
        return username + "@" + hostname + ":" + port;
    }
    
    /**
     * 判断两个连接信息是否相等
     */
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ConnectionInfo that = (ConnectionInfo) o;
        return port == that.port &&
               Objects.equals(hostname, that.hostname) &&
               Objects.equals(username, that.username);
    }
    
    /**
     * 计算哈希码
     */
    @Override
    public int hashCode() {
        return Objects.hash(hostname, port, username);
    }
}