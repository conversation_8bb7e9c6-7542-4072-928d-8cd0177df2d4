package com.sshtool.model;

/**
 * 服务器统计信息类，存储服务器的监控数据
 */
public class ServerStats {
    private double cpuUsage;        // CPU使用率 (%)
    private double memoryUsage;     // 内存使用率 (%)
    private double diskUsage;       // 磁盘使用率 (%)
    private double networkRx;       // 网络接收速率 (MB/s)
    private double networkTx;       // 网络发送速率 (MB/s)
    private long uptime;            // 系统运行时间 (秒)
    private int processCount;       // 进程数
    private double loadAverage;     // 系统负载
    private long totalMemory;       // 总内存 (MB)
    private long usedMemory;        // 已用内存 (MB)
    private long totalDisk;         // 总磁盘空间 (GB)
    private long usedDisk;          // 已用磁盘空间 (GB)
    private long timestamp;         // 数据采集时间戳
    
    /**
     * 构造函数，初始化服务器统计信息
     */
    public ServerStats() {
        this.cpuUsage = 0.0;
        this.memoryUsage = 0.0;
        this.diskUsage = 0.0;
        this.networkRx = 0.0;
        this.networkTx = 0.0;
        this.uptime = 0;
        this.processCount = 0;
        this.loadAverage = 0.0;
        this.totalMemory = 0;
        this.usedMemory = 0;
        this.totalDisk = 0;
        this.usedDisk = 0;
        this.timestamp = System.currentTimeMillis();
    }
    
    // Getter和Setter方法
    public double getCpuUsage() {
        return cpuUsage;
    }
    
    public void setCpuUsage(double cpuUsage) {
        this.cpuUsage = Math.max(0.0, Math.min(100.0, cpuUsage));
    }
    
    public double getMemoryUsage() {
        return memoryUsage;
    }
    
    public void setMemoryUsage(double memoryUsage) {
        this.memoryUsage = Math.max(0.0, Math.min(100.0, memoryUsage));
    }
    
    public double getDiskUsage() {
        return diskUsage;
    }
    
    public void setDiskUsage(double diskUsage) {
        this.diskUsage = Math.max(0.0, Math.min(100.0, diskUsage));
    }
    
    public double getNetworkRx() {
        return networkRx;
    }
    
    public void setNetworkRx(double networkRx) {
        this.networkRx = Math.max(0.0, networkRx);
    }
    
    public double getNetworkTx() {
        return networkTx;
    }
    
    public void setNetworkTx(double networkTx) {
        this.networkTx = Math.max(0.0, networkTx);
    }
    
    public long getUptime() {
        return uptime;
    }
    
    public void setUptime(long uptime) {
        this.uptime = Math.max(0, uptime);
    }
    
    public int getProcessCount() {
        return processCount;
    }
    
    public void setProcessCount(int processCount) {
        this.processCount = Math.max(0, processCount);
    }
    
    public double getLoadAverage() {
        return loadAverage;
    }
    
    public void setLoadAverage(double loadAverage) {
        this.loadAverage = Math.max(0.0, loadAverage);
    }
    
    public long getTotalMemory() {
        return totalMemory;
    }
    
    public void setTotalMemory(long totalMemory) {
        this.totalMemory = Math.max(0, totalMemory);
    }
    
    public long getUsedMemory() {
        return usedMemory;
    }
    
    public void setUsedMemory(long usedMemory) {
        this.usedMemory = Math.max(0, usedMemory);
    }
    
    public long getTotalDisk() {
        return totalDisk;
    }
    
    public void setTotalDisk(long totalDisk) {
        this.totalDisk = Math.max(0, totalDisk);
    }
    
    public long getUsedDisk() {
        return usedDisk;
    }
    
    public void setUsedDisk(long usedDisk) {
        this.usedDisk = Math.max(0, usedDisk);
    }
    
    public long getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }
    
    /**
     * 格式化运行时间为可读字符串
     */
    public String getFormattedUptime() {
        long days = uptime / 86400;
        long hours = (uptime % 86400) / 3600;
        long minutes = (uptime % 3600) / 60;
        
        if (days > 0) {
            return String.format("%d天 %d小时 %d分钟", days, hours, minutes);
        } else if (hours > 0) {
            return String.format("%d小时 %d分钟", hours, minutes);
        } else {
            return String.format("%d分钟", minutes);
        }
    }
    
    /**
     * 格式化内存使用情况
     */
    public String getFormattedMemory() {
        if (totalMemory > 0) {
            return String.format("%.1f GB / %.1f GB", 
                usedMemory / 1024.0, totalMemory / 1024.0);
        }
        return "N/A";
    }
    
    /**
     * 格式化磁盘使用情况
     */
    public String getFormattedDisk() {
        if (totalDisk > 0) {
            return String.format("%.1f GB / %.1f GB", 
                usedDisk / 1024.0, totalDisk / 1024.0);
        }
        return "N/A";
    }
    
    /**
     * 返回服务器统计信息的字符串表示
     */
    @Override
    public String toString() {
        return "ServerStats{" +
               "cpuUsage=" + String.format("%.1f", cpuUsage) + "%" +
               ", memoryUsage=" + String.format("%.1f", memoryUsage) + "%" +
               ", diskUsage=" + String.format("%.1f", diskUsage) + "%" +
               ", networkRx=" + String.format("%.2f", networkRx) + " MB/s" +
               ", networkTx=" + String.format("%.2f", networkTx) + " MB/s" +
               ", uptime=" + getFormattedUptime() +
               ", processCount=" + processCount +
               ", loadAverage=" + String.format("%.2f", loadAverage) +
               '}';
    }
}
