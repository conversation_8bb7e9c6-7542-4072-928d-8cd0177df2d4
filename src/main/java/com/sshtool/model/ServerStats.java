package com.sshtool.model;

/**
 * 服务器统计信息类，存储服务器的监控数据
 */
public class ServerStats {
    private double cpuUsage;        // CPU使用率 (%)
    private double memoryUsage;     // 内存使用率 (%)
    private double diskUsage;       // 磁盘使用率 (%)
    private double networkRx;       // 网络接收速率 (MB/s)
    private double networkTx;       // 网络发送速率 (MB/s)
    private long uptime;            // 系统运行时间 (秒)
    private int processCount;       // 进程数
    private int loadAverage;        // 系统负载
    
    /**
     * 构造函数，初始化服务器统计信息
     */
    public ServerStats() {
        this.cpuUsage = 0.0;
        this.memoryUsage = 0.0;
        this.diskUsage = 0.0;
        this.networkRx = 0.0;
        this.networkTx = 0.0;
        this.uptime = 0;
        this.processCount = 0;
        this.loadAverage = 0;
    }
    
    /**
     * 构造函数，初始化服务器统计信息
     * @param cpuUsage CPU使用率
     * @param memoryUsage 内存使用率
     * @param diskUsage 磁盘使用率
     * @param networkRx 网络接收速率
     * @param networkTx 网络发送速率
     */
    public ServerStats(double cpuUsage, double memoryUsage, double diskUsage, double networkRx, double networkTx) {
        this.cpuUsage = cpuUsage;
        this.memoryUsage = memoryUsage;
        this.diskUsage = diskUsage;
        this.networkRx = networkRx;
        this.networkTx = networkTx;
        this.uptime = 0;
        this.processCount = 0;
        this.loadAverage = 0;
    }
    
    // Getters and Setters
    public double getCpuUsage() {
        return cpuUsage;
    }
    
    public void setCpuUsage(double cpuUsage) {
        this.cpuUsage = cpuUsage;
    }
    
    public double getMemoryUsage() {
        return memoryUsage;
    }
    
    public void setMemoryUsage(double memoryUsage) {
        this.memoryUsage = memoryUsage;
    }
    
    public double getDiskUsage() {
        return diskUsage;
    }
    
    public void setDiskUsage(double diskUsage) {
        this.diskUsage = diskUsage;
    }
    
    public double getNetworkRx() {
        return networkRx;
    }
    
    public void setNetworkRx(double networkRx) {
        this.networkRx = networkRx;
    }
    
    public double getNetworkTx() {
        return networkTx;
    }
    
    public void setNetworkTx(double networkTx) {
        this.networkTx = networkTx;
    }
    
    public long getUptime() {
        return uptime;
    }
    
    public void setUptime(long uptime) {
        this.uptime = uptime;
    }
    
    public int getProcessCount() {
        return processCount;
    }
    
    public void setProcessCount(int processCount) {
        this.processCount = processCount;
    }
    
    public int getLoadAverage() {
        return loadAverage;
    }
    
    public void setLoadAverage(int loadAverage) {
        this.loadAverage = loadAverage;
    }
    
    /**
     * 返回服务器统计信息的字符串表示
     */
    @Override
    public String toString() {
        return "ServerStats{" +
               "cpuUsage=" + cpuUsage + "%" +
               ", memoryUsage=" + memoryUsage + "%" +
               ", diskUsage=" + diskUsage + "%" +
               ", networkRx=" + networkRx + " MB/s" +
               ", networkTx=" + networkTx + " MB/s" +
               ", uptime=" + uptime + "s" +
               ", processCount=" + processCount +
               ", loadAverage=" + loadAverage +
               '}';
    }
}