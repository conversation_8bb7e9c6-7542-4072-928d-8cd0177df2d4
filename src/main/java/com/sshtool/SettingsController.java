package com.sshtool;

import javafx.beans.value.ChangeListener;
import javafx.beans.value.ObservableValue;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.stage.Stage;

import java.io.File;import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.URL;
import java.util.Properties;
import java.util.ResourceBundle;

/**
 * 设置控制器类，管理应用程序的设置界面
 */
public class SettingsController implements Initializable {

    @FXML
    private ComboBox<String> themeComboBox;
    @FXML
    private Slider fontSizeSlider;
    @FXML
    private Label fontSizeLabel;
    @FXML
    private Spinner<Integer> historySizeSpinner;

    @FXML
    private TextField defaultPortField;
    @FXML
    private Spinner<Integer> timeoutSpinner;
    @FXML
    private ComboBox<String> hostKeyCheckingComboBox;
    @FXML
    private CheckBox compressionCheck;
    @FXML
    private Spinner<Integer> compressionLevelSpinner;

    @FXML
    private Spinner<Integer> monitoringIntervalSpinner;
    @FXML
    private CheckBox cpuMonitorCheck;
    @FXML
    private CheckBox memoryMonitorCheck;
    @FXML
    private CheckBox diskMonitorCheck;
    @FXML
    private CheckBox networkMonitorCheck;

    private Properties settingsProps;
    private static final String SETTINGS_FILE = System.getProperty("user.home") + File.separator + ".java-ssh-client" + File.separator + "settings.properties";

    @Override
    public void initialize(URL url, ResourceBundle resourceBundle) {
        // 初始化属性对象
        settingsProps = new Properties();
        
        // 加载已保存的设置
        loadSettings();
        
        // 设置字体大小滑块的监听器
        fontSizeSlider.valueProperty().addListener((observable, oldValue, newValue) -> {
            int fontSize = newValue.intValue();
            fontSizeLabel.setText(fontSize + "px");
        });
        
        // 设置压缩复选框的监听器
        compressionCheck.selectedProperty().addListener((observable, oldValue, newValue) -> {
            compressionLevelSpinner.setDisable(!newValue);
        });
    }

    /**
     * 加载已保存的设置
     */
    private void loadSettings() {
        File settingsFile = new File(SETTINGS_FILE);
        if (settingsFile.exists()) {
            try (FileInputStream fis = new FileInputStream(settingsFile)) {
                settingsProps.load(fis);
                
                // 加载通用设置
                String theme = settingsProps.getProperty("theme", "Light");
                themeComboBox.setValue(theme);
                
                int fontSize = Integer.parseInt(settingsProps.getProperty("fontSize", "14"));
                fontSizeSlider.setValue(fontSize);
                fontSizeLabel.setText(fontSize + "px");
                
                int historySize = Integer.parseInt(settingsProps.getProperty("historySize", "100"));
                historySizeSpinner.getValueFactory().setValue(historySize);
                
                // 加载连接设置
                String defaultPort = settingsProps.getProperty("defaultPort", "22");
                defaultPortField.setText(defaultPort);
                
                int timeout = Integer.parseInt(settingsProps.getProperty("timeout", "30"));
                timeoutSpinner.getValueFactory().setValue(timeout);
                
                String hostKeyChecking = settingsProps.getProperty("hostKeyChecking", "no");
                hostKeyCheckingComboBox.setValue(hostKeyChecking);
                
                boolean compression = Boolean.parseBoolean(settingsProps.getProperty("compression", "true"));
                compressionCheck.setSelected(compression);
                compressionLevelSpinner.setDisable(!compression);
                
                int compressionLevel = Integer.parseInt(settingsProps.getProperty("compressionLevel", "9"));
                compressionLevelSpinner.getValueFactory().setValue(compressionLevel);
                
                // 加载监控设置
                int monitoringInterval = Integer.parseInt(settingsProps.getProperty("monitoringInterval", "2"));
                monitoringIntervalSpinner.getValueFactory().setValue(monitoringInterval);
                
                boolean cpuMonitor = Boolean.parseBoolean(settingsProps.getProperty("cpuMonitor", "true"));
                cpuMonitorCheck.setSelected(cpuMonitor);
                
                boolean memoryMonitor = Boolean.parseBoolean(settingsProps.getProperty("memoryMonitor", "true"));
                memoryMonitorCheck.setSelected(memoryMonitor);
                
                boolean diskMonitor = Boolean.parseBoolean(settingsProps.getProperty("diskMonitor", "true"));
                diskMonitorCheck.setSelected(diskMonitor);
                
                boolean networkMonitor = Boolean.parseBoolean(settingsProps.getProperty("networkMonitor", "true"));
                networkMonitorCheck.setSelected(networkMonitor);
                
            } catch (IOException e) {
                System.err.println("Failed to load settings: " + e.getMessage());
            }
        }
    }

    /**
     * 保存设置
     */
    private void saveSettingsToFile() {
        // 确保配置目录存在
        File configDir = new File(System.getProperty("user.home") + File.separator + ".java-ssh-client");
        if (!configDir.exists()) {
            configDir.mkdirs();
        }
        
        // 保存设置到属性对象
        settingsProps.setProperty("theme", themeComboBox.getValue());
        settingsProps.setProperty("fontSize", String.valueOf((int) fontSizeSlider.getValue()));
        settingsProps.setProperty("historySize", historySizeSpinner.getValue().toString());
        
        settingsProps.setProperty("defaultPort", defaultPortField.getText());
        settingsProps.setProperty("timeout", timeoutSpinner.getValue().toString());
        settingsProps.setProperty("hostKeyChecking", hostKeyCheckingComboBox.getValue());
        settingsProps.setProperty("compression", String.valueOf(compressionCheck.isSelected()));
        settingsProps.setProperty("compressionLevel", compressionLevelSpinner.getValue().toString());
        
        settingsProps.setProperty("monitoringInterval", monitoringIntervalSpinner.getValue().toString());
        settingsProps.setProperty("cpuMonitor", String.valueOf(cpuMonitorCheck.isSelected()));
        settingsProps.setProperty("memoryMonitor", String.valueOf(memoryMonitorCheck.isSelected()));
        settingsProps.setProperty("diskMonitor", String.valueOf(diskMonitorCheck.isSelected()));
        settingsProps.setProperty("networkMonitor", String.valueOf(networkMonitorCheck.isSelected()));
        
        // 保存到文件
        try (FileOutputStream fos = new FileOutputStream(SETTINGS_FILE)) {
            settingsProps.store(fos, "Java SSH Client Settings");
        } catch (IOException e) {
            System.err.println("Failed to save settings: " + e.getMessage());
        }
    }

    /**
     * 应用设置
     */
    @FXML
    private void applySettings() {
        saveSettingsToFile();
        applySettingsToApplication();
        showAlert(Alert.AlertType.INFORMATION, "Settings Applied", "Your settings have been applied successfully.");
    }

    /**
     * 保存设置并关闭对话框
     */
    @FXML
    private void saveSettings() {
        saveSettingsToFile();
        applySettingsToApplication();
        closeDialog();
    }

    /**
     * 取消设置并关闭对话框
     */
    @FXML
    private void cancelSettings() {
        closeDialog();
    }

    /**
     * 应用设置到应用程序
     */
    private void applySettingsToApplication() {
        // 这里可以实现将设置应用到应用程序的逻辑
        // 例如更改主题、字体大小等
        
        // 注意：某些设置（如监控间隔）可能需要在MainController中应用
    }

    /**
     * 关闭设置对话框
     */
    private void closeDialog() {
        Stage stage = (Stage) themeComboBox.getScene().getWindow();
        stage.close();
    }

    /**
     * 显示警告对话框
     */
    private void showAlert(Alert.AlertType alertType, String title, String message) {
        Alert alert = new Alert(alertType);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    /**
     * 获取设置属性对象
     * @return 设置属性对象
     */
    public static Properties getSettings() {
        Properties props = new Properties();
        File settingsFile = new File(SETTINGS_FILE);
        if (settingsFile.exists()) {
            try (FileInputStream fis = new FileInputStream(settingsFile)) {
                props.load(fis);
            } catch (IOException e) {
                System.err.println("Failed to load settings: " + e.getMessage());
            }
        }
        return props;
    }

    /**
     * 获取设置文件的路径
     * @return 设置文件路径
     */
    public static String getSettingsFilePath() {
        return SETTINGS_FILE;
    }
}