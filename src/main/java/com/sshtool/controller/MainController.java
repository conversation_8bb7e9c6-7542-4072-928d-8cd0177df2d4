package com.sshtool.controller;

import com.jcraft.jsch.*;
import com.sshtool.model.ConnectionInfo;
import com.sshtool.model.FileItem;
import com.sshtool.model.ServerStats;
import com.sshtool.service.ConnectionManager;
import com.sshtool.service.FileManager;
import com.sshtool.service.HistoryManager;
import com.sshtool.service.MonitoringService;
import javafx.application.Platform;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.concurrent.Task;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.input.KeyCode;
import javafx.stage.FileChooser;
import javafx.stage.Modality;
import javafx.stage.Stage;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.*;
import java.net.URL;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import java.util.ResourceBundle;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 主控制器类，处理界面交互和业务逻辑
 */
public class MainController implements Initializable {
    private static final Logger logger = LogManager.getLogger(MainController.class);

    // FXML注入的UI组件
    @FXML private TabPane mainTabPane;
    @FXML private Tab connectionTab, consoleTab, fileManagerTab, monitoringTab;
    
    // 连接相关组件
    @FXML private ComboBox<ConnectionInfo> savedConnections;
    @FXML private TextField hostnameField, portField, usernameField, nicknameField;
    @FXML private PasswordField passwordField, passphraseField;
    @FXML private TextField privateKeyField;
    @FXML private RadioButton passwordAuthRadio, keyAuthRadio;
    @FXML private CheckBox saveConnectionCheck;
    @FXML private Label connectionStatusLabel;
    
    // 终端相关组件
    @FXML private TextArea consoleOutput;
    @FXML private TextField commandInput;
    
    // 文件管理相关组件
    @FXML private TreeView<String> fileTree;
    @FXML private TableView<FileItem> fileTable;
    @FXML private TableColumn<FileItem, String> fileNameColumn, fileSizeColumn, fileModifiedColumn, filePermissionsColumn;
    @FXML private TextField currentPathField;
    
    // 监控相关组件
    @FXML private ProgressBar cpuUsageBar, memoryUsageBar, diskUsageBar;
    @FXML private Label cpuUsageLabel, memoryUsageLabel, diskUsageLabel;
    @FXML private Label networkRxLabel, networkTxLabel, uptimeLabel, processCountLabel, loadAverageLabel;
    @FXML private Button startMonitoringButton, stopMonitoringButton;
    @FXML private CheckBox autoStartMonitoringCheck;
    
    // 状态栏组件
    @FXML private Label statusLabel, connectionInfoLabel, timeLabel;

    // 服务类
    private ConnectionManager connectionManager;
    private FileManager fileManager;
    private HistoryManager historyManager;
    private MonitoringService monitoringService;
    private ExecutorService executorService;

    // 连接状态
    private Session currentSession;
    private ChannelShell currentChannel;
    private PrintWriter channelWriter;
    private ObservableList<ConnectionInfo> connectionList;
    private String currentDirectory = "/";

    @Override
    public void initialize(URL url, ResourceBundle resourceBundle) {
        logger.info("Initializing MainController");
        
        // 初始化服务
        connectionManager = new ConnectionManager();
        fileManager = new FileManager();
        historyManager = new HistoryManager();
        monitoringService = new MonitoringService();
        executorService = Executors.newCachedThreadPool();
        
        // 初始化UI组件
        initializeUI();
        
        // 加载保存的连接
        loadSavedConnections();
        
        // 启动状态更新定时器
        startStatusTimer();
        
        logger.info("MainController initialized successfully");
    }

    /**
     * 初始化UI组件
     */
    private void initializeUI() {
        // 设置认证方式切换
        ToggleGroup authGroup = new ToggleGroup();
        passwordAuthRadio.setToggleGroup(authGroup);
        keyAuthRadio.setToggleGroup(authGroup);
        
        passwordAuthRadio.selectedProperty().addListener((obs, oldVal, newVal) -> {
            passwordField.getParent().setDisable(!newVal);
            privateKeyField.getParent().setDisable(newVal);
        });
        
        // 设置命令输入回车事件
        commandInput.setOnKeyPressed(event -> {
            if (event.getCode() == KeyCode.ENTER) {
                sendCommand();
            }
        });
        
        // 初始化文件表格
        fileNameColumn.setCellValueFactory(new PropertyValueFactory<>("name"));
        fileSizeColumn.setCellValueFactory(new PropertyValueFactory<>("formattedSize"));
        fileModifiedColumn.setCellValueFactory(new PropertyValueFactory<>("lastModified"));
        filePermissionsColumn.setCellValueFactory(new PropertyValueFactory<>("permissions"));
        
        // 设置文件表格双击事件
        fileTable.setRowFactory(tv -> {
            TableRow<FileItem> row = new TableRow<>();
            row.setOnMouseClicked(event -> {
                if (event.getClickCount() == 2 && !row.isEmpty()) {
                    FileItem item = row.getItem();
                    if (item.isDirectory()) {
                        navigateToDirectory(item.getPath());
                    }
                }
            });
            return row;
        });
        
        // 初始化连接下拉框
        connectionList = FXCollections.observableArrayList();
        savedConnections.setItems(connectionList);
        savedConnections.setOnAction(e -> loadSelectedConnection());
        
        // 设置初始状态
        updateConnectionStatus("未连接");
        disableConnectedFeatures(true);
    }

    /**
     * 加载保存的连接
     */
    private void loadSavedConnections() {
        List<ConnectionInfo> connections = historyManager.loadConnections();
        connectionList.clear();
        connectionList.addAll(connections);
    }

    /**
     * 加载选中的连接信息
     */
    private void loadSelectedConnection() {
        ConnectionInfo selected = savedConnections.getSelectionModel().getSelectedItem();
        if (selected != null) {
            hostnameField.setText(selected.getHostname());
            portField.setText(String.valueOf(selected.getPort()));
            usernameField.setText(selected.getUsername());
            nicknameField.setText(selected.getNickname());
            
            if (selected.isUsePrivateKey()) {
                keyAuthRadio.setSelected(true);
                privateKeyField.setText(selected.getPrivateKeyPath());
                passphraseField.setText(selected.getPassphrase());
            } else {
                passwordAuthRadio.setSelected(true);
                passwordField.setText(selected.getPassword());
            }
        }
    }

    /**
     * 连接到SSH服务器
     */
    @FXML
    private void connect() {
        if (currentSession != null && currentSession.isConnected()) {
            showAlert("警告", "已经存在活动连接，请先断开当前连接。");
            return;
        }

        ConnectionInfo connectionInfo = createConnectionInfoFromUI();
        if (!connectionInfo.isValid()) {
            showAlert("错误", "请填写完整的连接信息。");
            return;
        }

        updateStatus("正在连接...");
        
        Task<Void> connectTask = new Task<Void>() {
            @Override
            protected Void call() throws Exception {
                try {
                    // 创建SSH会话
                    currentSession = connectionManager.createSession(connectionInfo);
                    connectionManager.connect(currentSession);
                    
                    // 创建Shell通道
                    currentChannel = connectionManager.createShellChannel(currentSession);
                    
                    // 设置输入输出流
                    PipedInputStream pipeIn = new PipedInputStream();
                    PipedOutputStream pipeOut = new PipedOutputStream(pipeIn);
                    currentChannel.setInputStream(pipeIn);
                    currentChannel.setOutputStream(System.out);
                    
                    channelWriter = new PrintWriter(pipeOut, true);
                    currentChannel.connect();
                    
                    Platform.runLater(() -> {
                        updateConnectionStatus("已连接: " + connectionInfo.toString());
                        disableConnectedFeatures(false);
                        mainTabPane.getSelectionModel().select(consoleTab);
                        
                        // 保存连接信息
                        if (saveConnectionCheck.isSelected()) {
                            historyManager.saveConnection(connectionInfo);
                            loadSavedConnections();
                        }
                        
                        // 自动开始监控
                        if (autoStartMonitoringCheck.isSelected()) {
                            startMonitoring();
                        }
                        
                        // 加载文件系统
                        loadFileSystem();
                    });
                    
                    // 读取终端输出
                    readTerminalOutput();
                    
                } catch (Exception e) {
                    Platform.runLater(() -> {
                        updateStatus("连接失败: " + e.getMessage());
                        showAlert("连接失败", "无法连接到服务器: " + e.getMessage());
                    });
                    throw e;
                }
                return null;
            }
        };
        
        executorService.submit(connectTask);
    }

    /**
     * 断开SSH连接
     */
    @FXML
    private void disconnect() {
        if (currentChannel != null && currentChannel.isConnected()) {
            currentChannel.disconnect();
        }
        if (currentSession != null && currentSession.isConnected()) {
            currentSession.disconnect();
        }
        
        monitoringService.stopMonitoring();
        
        updateConnectionStatus("未连接");
        disableConnectedFeatures(true);
        updateStatus("已断开连接");
        
        appendToConsole("\n=== 连接已断开 ===\n");
    }

    /**
     * 发送命令到终端
     */
    @FXML
    private void sendCommand() {
        String command = commandInput.getText().trim();
        if (command.isEmpty() || channelWriter == null) {
            return;
        }
        
        // 保存命令历史
        historyManager.saveCommandHistory(command);
        
        // 发送命令
        channelWriter.println(command);
        channelWriter.flush();
        
        // 清空输入框
        commandInput.clear();
        
        // 在控制台显示命令
        appendToConsole("$ " + command + "\n");
    }

    /**
     * 清空控制台
     */
    @FXML
    private void clearConsole() {
        consoleOutput.clear();
    }

    /**
     * 开始系统监控
     */
    @FXML
    private void startMonitoring() {
        if (currentSession == null || !currentSession.isConnected()) {
            showAlert("错误", "请先建立SSH连接。");
            return;
        }
        
        monitoringService.startMonitoring(currentSession, this::updateMonitoringData);
        startMonitoringButton.setDisable(true);
        stopMonitoringButton.setDisable(false);
        updateStatus("监控已启动");
    }

    /**
     * 停止系统监控
     */
    @FXML
    private void stopMonitoring() {
        monitoringService.stopMonitoring();
        startMonitoringButton.setDisable(false);
        stopMonitoringButton.setDisable(true);
        updateStatus("监控已停止");
    }

    /**
     * 更新监控数据
     */
    private void updateMonitoringData(ServerStats stats) {
        Platform.runLater(() -> {
            cpuUsageBar.setProgress(stats.getCpuUsage() / 100.0);
            cpuUsageLabel.setText(String.format("%.1f%%", stats.getCpuUsage()));
            
            memoryUsageBar.setProgress(stats.getMemoryUsage() / 100.0);
            memoryUsageLabel.setText(String.format("%.1f%%", stats.getMemoryUsage()));
            
            diskUsageBar.setProgress(stats.getDiskUsage() / 100.0);
            diskUsageLabel.setText(String.format("%.1f%%", stats.getDiskUsage()));
            
            networkRxLabel.setText(String.format("%.2f MB/s", stats.getNetworkRx()));
            networkTxLabel.setText(String.format("%.2f MB/s", stats.getNetworkTx()));
            
            uptimeLabel.setText(stats.getFormattedUptime());
            processCountLabel.setText(String.valueOf(stats.getProcessCount()));
            loadAverageLabel.setText(String.format("%.2f", stats.getLoadAverage()));
        });
    }

    /**
     * 从UI创建连接信息对象
     */
    private ConnectionInfo createConnectionInfoFromUI() {
        ConnectionInfo info = new ConnectionInfo();
        info.setHostname(hostnameField.getText().trim());
        info.setPort(Integer.parseInt(portField.getText().trim()));
        info.setUsername(usernameField.getText().trim());
        info.setNickname(nicknameField.getText().trim());
        
        if (passwordAuthRadio.isSelected()) {
            info.setUsePrivateKey(false);
            info.setPassword(passwordField.getText());
        } else {
            info.setUsePrivateKey(true);
            info.setPrivateKeyPath(privateKeyField.getText().trim());
            info.setPassphrase(passphraseField.getText());
        }
        
        return info;
    }

    /**
     * 读取终端输出
     */
    private void readTerminalOutput() {
        executorService.submit(() -> {
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(currentChannel.getInputStream()))) {
                
                char[] buffer = new char[1024];
                int bytesRead;
                
                while (currentChannel.isConnected() && 
                       (bytesRead = reader.read(buffer)) != -1) {
                    
                    String output = new String(buffer, 0, bytesRead);
                    Platform.runLater(() -> appendToConsole(output));
                }
            } catch (IOException e) {
                logger.error("Error reading terminal output: {}", e.getMessage());
            }
        });
    }

    /**
     * 向控制台追加文本
     */
    private void appendToConsole(String text) {
        consoleOutput.appendText(text);
        consoleOutput.setScrollTop(Double.MAX_VALUE);
    }

    /**
     * 加载文件系统
     */
    private void loadFileSystem() {
        if (currentSession == null || !currentSession.isConnected()) {
            return;
        }
        
        executorService.submit(() -> {
            try {
                List<FileItem> files = fileManager.listDirectory(currentSession, currentDirectory);
                Platform.runLater(() -> {
                    fileTable.getItems().clear();
                    fileTable.getItems().addAll(files);
                    currentPathField.setText(currentDirectory);
                });
            } catch (Exception e) {
                Platform.runLater(() -> 
                    showAlert("错误", "无法加载文件列表: " + e.getMessage()));
            }
        });
    }

    /**
     * 导航到指定目录
     */
    private void navigateToDirectory(String path) {
        currentDirectory = path;
        loadFileSystem();
    }

    /**
     * 更新连接状态
     */
    private void updateConnectionStatus(String status) {
        connectionStatusLabel.setText(status);
        connectionInfoLabel.setText(status);
    }

    /**
     * 更新状态栏
     */
    private void updateStatus(String status) {
        statusLabel.setText(status);
    }

    /**
     * 启用/禁用连接相关功能
     */
    private void disableConnectedFeatures(boolean disable) {
        consoleTab.setDisable(disable);
        fileManagerTab.setDisable(disable);
        monitoringTab.setDisable(disable);
        commandInput.setDisable(disable);
    }

    /**
     * 启动状态更新定时器
     */
    private void startStatusTimer() {
        executorService.submit(() -> {
            while (true) {
                try {
                    Thread.sleep(1000);
                    Platform.runLater(() -> {
                        String currentTime = LocalDateTime.now()
                            .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                        timeLabel.setText(currentTime);
                    });
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        });
    }

    /**
     * 显示警告对话框
     */
    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.WARNING);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    /**
     * 新建连接
     */
    @FXML
    private void newConnection() {
        // 清空连接表单
        hostnameField.clear();
        portField.setText("22");
        usernameField.clear();
        nicknameField.clear();
        passwordField.clear();
        privateKeyField.clear();
        passphraseField.clear();
        passwordAuthRadio.setSelected(true);
        saveConnectionCheck.setSelected(true);

        // 切换到连接标签页
        mainTabPane.getSelectionModel().select(connectionTab);
    }

    /**
     * 导入连接配置
     */
    @FXML
    private void importConnections() {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("导入连接配置");
        fileChooser.getExtensionFilters().add(
            new FileChooser.ExtensionFilter("JSON文件", "*.json"));

        Stage stage = (Stage) savedConnections.getScene().getWindow();
        java.io.File file = fileChooser.showOpenDialog(stage);

        if (file != null) {
            try {
                historyManager.importConnections(file);
                loadSavedConnections();
                showAlert("成功", "连接配置导入成功！");
            } catch (Exception e) {
                showAlert("错误", "导入失败: " + e.getMessage());
            }
        }
    }

    /**
     * 导出连接配置
     */
    @FXML
    private void exportConnections() {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("导出连接配置");
        fileChooser.setInitialFileName("ssh-connections.json");
        fileChooser.getExtensionFilters().add(
            new FileChooser.ExtensionFilter("JSON文件", "*.json"));

        Stage stage = (Stage) savedConnections.getScene().getWindow();
        java.io.File file = fileChooser.showSaveDialog(stage);

        if (file != null) {
            try {
                historyManager.exportConnections(file);
                showAlert("成功", "连接配置导出成功！");
            } catch (Exception e) {
                showAlert("错误", "导出失败: " + e.getMessage());
            }
        }
    }

    /**
     * 打开设置窗口
     */
    @FXML
    private void openSettings() {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/com/sshtool/settings-view.fxml"));
            Parent root = loader.load();

            Stage settingsStage = new Stage();
            settingsStage.setTitle("设置");
            settingsStage.setScene(new Scene(root));
            settingsStage.initModality(Modality.APPLICATION_MODAL);
            settingsStage.setResizable(false);

            // 应用CSS样式
            settingsStage.getScene().getStylesheets().add(
                getClass().getResource("/css/modern-theme.css").toExternalForm());

            settingsStage.showAndWait();
        } catch (Exception e) {
            showAlert("错误", "无法打开设置窗口: " + e.getMessage());
        }
    }

    /**
     * 退出应用程序
     */
    @FXML
    private void exitApplication() {
        // 断开连接
        if (currentSession != null && currentSession.isConnected()) {
            disconnect();
        }
        Platform.exit();
    }

    /**
     * 重新连接
     */
    @FXML
    private void reconnect() {
        if (currentSession != null) {
            disconnect();
            // 等待一秒后重新连接
            executorService.submit(() -> {
                try {
                    Thread.sleep(1000);
                    Platform.runLater(this::connect);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            });
        }
    }

    /**
     * 测试连接
     */
    @FXML
    private void testConnection() {
        ConnectionInfo connectionInfo = createConnectionInfoFromUI();
        if (!connectionInfo.isValid()) {
            showAlert("错误", "请填写完整的连接信息。");
            return;
        }

        updateStatus("正在测试连接...");

        Task<Boolean> testTask = new Task<Boolean>() {
            @Override
            protected Boolean call() throws Exception {
                return connectionManager.testConnection(connectionInfo);
            }
        };

        testTask.setOnSucceeded(e -> {
            boolean success = testTask.getValue();
            if (success) {
                updateStatus("连接测试成功");
                showAlert("成功", "连接测试成功！");
            } else {
                updateStatus("连接测试失败");
                showAlert("失败", "连接测试失败，请检查连接信息。");
            }
        });

        testTask.setOnFailed(e -> {
            updateStatus("连接测试失败");
            Throwable exception = testTask.getException();
            showAlert("错误", "连接测试失败: " + exception.getMessage());
        });

        executorService.submit(testTask);
    }

    /**
     * 清空命令历史
     */
    @FXML
    private void clearCommandHistory() {
        Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
        alert.setTitle("确认");
        alert.setHeaderText("清空命令历史");
        alert.setContentText("确定要清空所有命令历史记录吗？");

        Optional<ButtonType> result = alert.showAndWait();
        if (result.isPresent() && result.get() == ButtonType.OK) {
            historyManager.clearCommandHistory();
            showAlert("成功", "命令历史已清空。");
        }
    }

    /**
     * 显示系统信息
     */
    @FXML
    private void showSystemInfo() {
        if (currentSession == null || !currentSession.isConnected()) {
            showAlert("错误", "请先建立SSH连接。");
            return;
        }

        executorService.submit(() -> {
            try {
                StringBuilder info = new StringBuilder();
                info.append("=== 系统信息 ===\n");
                info.append("主机名: ").append(connectionManager.executeCommand(currentSession, "hostname")).append("\n");
                info.append("操作系统: ").append(connectionManager.executeCommand(currentSession, "uname -a")).append("\n");
                info.append("内核版本: ").append(connectionManager.executeCommand(currentSession, "uname -r")).append("\n");
                info.append("CPU信息: ").append(connectionManager.executeCommand(currentSession, "cat /proc/cpuinfo | grep 'model name' | head -1 | cut -d: -f2")).append("\n");
                info.append("内存信息: ").append(connectionManager.executeCommand(currentSession, "free -h | grep Mem")).append("\n");
                info.append("磁盘信息: ").append(connectionManager.executeCommand(currentSession, "df -h /")).append("\n");

                Platform.runLater(() -> {
                    Alert alert = new Alert(Alert.AlertType.INFORMATION);
                    alert.setTitle("系统信息");
                    alert.setHeaderText("服务器系统信息");
                    alert.setContentText(info.toString());
                    alert.getDialogPane().setPrefWidth(600);
                    alert.showAndWait();
                });
            } catch (Exception e) {
                Platform.runLater(() -> showAlert("错误", "获取系统信息失败: " + e.getMessage()));
            }
        });
    }

    /**
     * 显示关于对话框
     */
    @FXML
    private void showAbout() {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("关于");
        alert.setHeaderText("Java SSH Client v1.0.0");
        alert.setContentText("一个基于JavaFX开发的现代化SSH客户端\n\n" +
                           "功能特性：\n" +
                           "• SSH连接管理\n" +
                           "• 交互式终端\n" +
                           "• 文件管理\n" +
                           "• 系统监控\n" +
                           "• 个性化设置\n\n" +
                           "开发者: Your Name\n" +
                           "许可证: MIT License");
        alert.showAndWait();
    }

    /**
     * 显示帮助信息
     */
    @FXML
    private void showHelp() {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("帮助");
        alert.setHeaderText("使用帮助");
        alert.setContentText("快速入门：\n\n" +
                           "1. 连接设置\n" +
                           "   - 在连接标签页填写服务器信息\n" +
                           "   - 选择密码或私钥认证方式\n" +
                           "   - 点击连接按钮建立连接\n\n" +
                           "2. 终端使用\n" +
                           "   - 在命令输入框输入命令\n" +
                           "   - 按回车键执行命令\n" +
                           "   - 支持命令历史记录\n\n" +
                           "3. 文件管理\n" +
                           "   - 浏览远程文件系统\n" +
                           "   - 上传下载文件\n" +
                           "   - 文件操作（删除、重命名等）\n\n" +
                           "4. 系统监控\n" +
                           "   - 实时监控服务器状态\n" +
                           "   - CPU、内存、磁盘使用情况\n" +
                           "   - 网络流量统计");
        alert.getDialogPane().setPrefWidth(500);
        alert.showAndWait();
    }

    /**
     * 浏览私钥文件
     */
    @FXML
    private void browsePrivateKey() {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("选择私钥文件");
        fileChooser.getExtensionFilters().addAll(
            new FileChooser.ExtensionFilter("私钥文件", "*.pem", "*.key", "*.ppk"),
            new FileChooser.ExtensionFilter("所有文件", "*.*"));

        Stage stage = (Stage) privateKeyField.getScene().getWindow();
        java.io.File file = fileChooser.showOpenDialog(stage);

        if (file != null) {
            privateKeyField.setText(file.getAbsolutePath());
        }
    }

    /**
     * 刷新文件列表
     */
    @FXML
    private void refreshFileList() {
        loadFileSystem();
    }

    /**
     * 上传文件
     */
    @FXML
    private void uploadFile() {
        if (currentSession == null || !currentSession.isConnected()) {
            showAlert("错误", "请先建立SSH连接。");
            return;
        }

        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("选择要上传的文件");

        Stage stage = (Stage) fileTable.getScene().getWindow();
        java.io.File file = fileChooser.showOpenDialog(stage);

        if (file != null) {
            String remotePath = currentDirectory + "/" + file.getName();

            Task<Void> uploadTask = new Task<Void>() {
                @Override
                protected Void call() throws Exception {
                    fileManager.uploadFile(currentSession, file, remotePath, new FileManager.ProgressCallback() {
                        @Override
                        public void onProgress(long transferred, long total) {
                            double progress = (double) transferred / total;
                            Platform.runLater(() -> updateStatus(String.format("上传进度: %.1f%%", progress * 100)));
                        }

                        @Override
                        public void onComplete() {
                            Platform.runLater(() -> {
                                updateStatus("文件上传完成");
                                loadFileSystem();
                            });
                        }
                    });
                    return null;
                }
            };

            uploadTask.setOnFailed(e -> {
                Throwable exception = uploadTask.getException();
                Platform.runLater(() -> showAlert("错误", "文件上传失败: " + exception.getMessage()));
            });

            executorService.submit(uploadTask);
        }
    }

    /**
     * 创建目录
     */
    @FXML
    private void createDirectory() {
        if (currentSession == null || !currentSession.isConnected()) {
            showAlert("错误", "请先建立SSH连接。");
            return;
        }

        TextInputDialog dialog = new TextInputDialog();
        dialog.setTitle("创建目录");
        dialog.setHeaderText("新建文件夹");
        dialog.setContentText("请输入文件夹名称:");

        Optional<String> result = dialog.showAndWait();
        if (result.isPresent() && !result.get().trim().isEmpty()) {
            String dirName = result.get().trim();
            String dirPath = currentDirectory + "/" + dirName;

            executorService.submit(() -> {
                try {
                    fileManager.createDirectory(currentSession, dirPath);
                    Platform.runLater(() -> {
                        updateStatus("目录创建成功");
                        loadFileSystem();
                    });
                } catch (Exception e) {
                    Platform.runLater(() -> showAlert("错误", "创建目录失败: " + e.getMessage()));
                }
            });
        }
    }

    /**
     * 下载文件
     */
    @FXML
    private void downloadFile() {
        FileItem selectedItem = fileTable.getSelectionModel().getSelectedItem();
        if (selectedItem == null) {
            showAlert("提示", "请选择要下载的文件。");
            return;
        }

        if (selectedItem.isDirectory()) {
            showAlert("提示", "不能下载目录，请选择文件。");
            return;
        }

        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("保存文件");
        fileChooser.setInitialFileName(selectedItem.getName());

        Stage stage = (Stage) fileTable.getScene().getWindow();
        java.io.File file = fileChooser.showSaveDialog(stage);

        if (file != null) {
            Task<Void> downloadTask = new Task<Void>() {
                @Override
                protected Void call() throws Exception {
                    fileManager.downloadFile(currentSession, selectedItem.getPath(), file,
                        new FileManager.ProgressCallback() {
                            @Override
                            public void onProgress(long transferred, long total) {
                                double progress = (double) transferred / total;
                                Platform.runLater(() -> updateStatus(String.format("下载进度: %.1f%%", progress * 100)));
                            }

                            @Override
                            public void onComplete() {
                                Platform.runLater(() -> updateStatus("文件下载完成"));
                            }
                        });
                    return null;
                }
            };

            downloadTask.setOnFailed(e -> {
                Throwable exception = downloadTask.getException();
                Platform.runLater(() -> showAlert("错误", "文件下载失败: " + exception.getMessage()));
            });

            executorService.submit(downloadTask);
        }
    }

    /**
     * 删除文件
     */
    @FXML
    private void deleteFile() {
        FileItem selectedItem = fileTable.getSelectionModel().getSelectedItem();
        if (selectedItem == null) {
            showAlert("提示", "请选择要删除的文件或目录。");
            return;
        }

        Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
        alert.setTitle("确认删除");
        alert.setHeaderText("删除" + (selectedItem.isDirectory() ? "目录" : "文件"));
        alert.setContentText("确定要删除 \"" + selectedItem.getName() + "\" 吗？\n" +
                           (selectedItem.isDirectory() ? "注意：目录及其所有内容将被删除！" : ""));

        Optional<ButtonType> result = alert.showAndWait();
        if (result.isPresent() && result.get() == ButtonType.OK) {
            executorService.submit(() -> {
                try {
                    fileManager.delete(currentSession, selectedItem.getPath(), selectedItem.isDirectory());
                    Platform.runLater(() -> {
                        updateStatus("删除成功");
                        loadFileSystem();
                    });
                } catch (Exception e) {
                    Platform.runLater(() -> showAlert("错误", "删除失败: " + e.getMessage()));
                }
            });
        }
    }

    /**
     * 重命名文件
     */
    @FXML
    private void renameFile() {
        FileItem selectedItem = fileTable.getSelectionModel().getSelectedItem();
        if (selectedItem == null) {
            showAlert("提示", "请选择要重命名的文件或目录。");
            return;
        }

        TextInputDialog dialog = new TextInputDialog(selectedItem.getName());
        dialog.setTitle("重命名");
        dialog.setHeaderText("重命名" + (selectedItem.isDirectory() ? "目录" : "文件"));
        dialog.setContentText("请输入新名称:");

        Optional<String> result = dialog.showAndWait();
        if (result.isPresent() && !result.get().trim().isEmpty()) {
            String newName = result.get().trim();
            if (!newName.equals(selectedItem.getName())) {
                String newPath = currentDirectory + "/" + newName;

                executorService.submit(() -> {
                    try {
                        fileManager.rename(currentSession, selectedItem.getPath(), newPath);
                        Platform.runLater(() -> {
                            updateStatus("重命名成功");
                            loadFileSystem();
                        });
                    } catch (Exception e) {
                        Platform.runLater(() -> showAlert("错误", "重命名失败: " + e.getMessage()));
                    }
                });
            }
        }
    }

    /**
     * 显示文件属性
     */
    @FXML
    private void showFileProperties() {
        FileItem selectedItem = fileTable.getSelectionModel().getSelectedItem();
        if (selectedItem == null) {
            showAlert("提示", "请选择要查看属性的文件或目录。");
            return;
        }

        StringBuilder properties = new StringBuilder();
        properties.append("=== 文件属性 ===\n");
        properties.append("名称: ").append(selectedItem.getName()).append("\n");
        properties.append("路径: ").append(selectedItem.getPath()).append("\n");
        properties.append("类型: ").append(selectedItem.isDirectory() ? "目录" : "文件").append("\n");
        properties.append("大小: ").append(selectedItem.getFormattedSize()).append("\n");
        properties.append("权限: ").append(selectedItem.getPermissions()).append("\n");
        properties.append("所有者: ").append(selectedItem.getOwner()).append("\n");
        properties.append("组: ").append(selectedItem.getGroup()).append("\n");
        properties.append("修改时间: ").append(selectedItem.getLastModified()).append("\n");

        if (selectedItem.isSymlink()) {
            properties.append("链接目标: ").append(selectedItem.getLinkTarget()).append("\n");
        }

        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("文件属性");
        alert.setHeaderText(selectedItem.getName() + " 的属性");
        alert.setContentText(properties.toString());
        alert.getDialogPane().setPrefWidth(500);
        alert.showAndWait();
    }
}
