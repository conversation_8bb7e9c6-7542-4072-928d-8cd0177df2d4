package com.sshtool.controller;

import com.sshtool.service.HistoryManager;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.stage.DirectoryChooser;
import javafx.stage.Stage;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.File;
import java.net.URL;
import java.util.Properties;
import java.util.ResourceBundle;

/**
 * 设置控制器类，处理应用程序设置
 */
public class SettingsController implements Initializable {
    private static final Logger logger = LogManager.getLogger(SettingsController.class);

    // 常规设置
    @FXML private ComboBox<String> themeComboBox;
    @FXML private ComboBox<String> languageComboBox;
    @FXML private CheckBox autoConnectLastCheck;
    @FXML private CheckBox minimizeToTrayCheck;
    @FXML private CheckBox checkUpdatesCheck;

    // 终端设置
    @FXML private ComboBox<String> fontFamilyComboBox;
    @FXML private Slider fontSizeSlider;
    @FXML private Label fontSizeLabel;
    @FXML private ComboBox<String> colorSchemeComboBox;
    @FXML private CheckBox saveCommandHistoryCheck;
    @FXML private Spinner<Integer> historyLimitSpinner;
    @FXML private CheckBox autoScrollCheck;
    @FXML private CheckBox wordWrapCheck;

    // 连接设置
    @FXML private Spinner<Integer> connectionTimeoutSpinner;
    @FXML private Spinner<Integer> readTimeoutSpinner;
    @FXML private CheckBox compressionCheck;
    @FXML private Spinner<Integer> compressionLevelSpinner;
    @FXML private CheckBox keepAliveCheck;
    @FXML private Spinner<Integer> keepAliveIntervalSpinner;
    @FXML private CheckBox strictHostKeyCheck;
    @FXML private CheckBox savePasswordCheck;
    @FXML private CheckBox autoCloseCheck;
    @FXML private Spinner<Integer> idleTimeoutSpinner;

    // 文件传输设置
    @FXML private TextField downloadDirField;
    @FXML private CheckBox overwriteFilesCheck;
    @FXML private CheckBox preserveTimestampCheck;
    @FXML private CheckBox showTransferProgressCheck;
    @FXML private Spinner<Integer> concurrentTransfersSpinner;
    @FXML private ComboBox<String> bufferSizeComboBox;

    // 监控设置
    @FXML private Spinner<Integer> monitoringIntervalSpinner;
    @FXML private CheckBox monitorCpuCheck;
    @FXML private CheckBox monitorMemoryCheck;
    @FXML private CheckBox monitorDiskCheck;
    @FXML private CheckBox monitorNetworkCheck;
    @FXML private CheckBox monitorProcessCheck;
    @FXML private CheckBox enableAlertsCheck;
    @FXML private Spinner<Integer> cpuAlertThresholdSpinner;
    @FXML private Spinner<Integer> memoryAlertThresholdSpinner;
    @FXML private Spinner<Integer> diskAlertThresholdSpinner;

    private HistoryManager historyManager;
    private Properties settings;
    private boolean settingsChanged = false;

    @Override
    public void initialize(URL url, ResourceBundle resourceBundle) {
        logger.info("Initializing SettingsController");
        
        historyManager = new HistoryManager();
        settings = historyManager.loadSettings();
        
        initializeUI();
        loadSettings();
        
        logger.info("SettingsController initialized successfully");
    }

    /**
     * 初始化UI组件
     */
    private void initializeUI() {
        // 设置字体大小滑块监听器
        fontSizeSlider.valueProperty().addListener((observable, oldValue, newValue) -> {
            int fontSize = newValue.intValue();
            fontSizeLabel.setText(fontSize + "px");
            settingsChanged = true;
        });
        
        // 设置压缩复选框监听器
        compressionCheck.selectedProperty().addListener((observable, oldValue, newValue) -> {
            compressionLevelSpinner.setDisable(!newValue);
            settingsChanged = true;
        });
        
        // 设置Keep-Alive复选框监听器
        keepAliveCheck.selectedProperty().addListener((observable, oldValue, newValue) -> {
            keepAliveIntervalSpinner.setDisable(!newValue);
            settingsChanged = true;
        });
        
        // 设置自动关闭复选框监听器
        autoCloseCheck.selectedProperty().addListener((observable, oldValue, newValue) -> {
            idleTimeoutSpinner.setDisable(!newValue);
            settingsChanged = true;
        });
        
        // 设置警告复选框监听器
        enableAlertsCheck.selectedProperty().addListener((observable, oldValue, newValue) -> {
            cpuAlertThresholdSpinner.setDisable(!newValue);
            memoryAlertThresholdSpinner.setDisable(!newValue);
            diskAlertThresholdSpinner.setDisable(!newValue);
            settingsChanged = true;
        });
        
        // 为所有控件添加变更监听器
        addChangeListeners();
    }

    /**
     * 为控件添加变更监听器
     */
    private void addChangeListeners() {
        // ComboBox监听器
        themeComboBox.setOnAction(e -> settingsChanged = true);
        languageComboBox.setOnAction(e -> settingsChanged = true);
        fontFamilyComboBox.setOnAction(e -> settingsChanged = true);
        colorSchemeComboBox.setOnAction(e -> settingsChanged = true);
        bufferSizeComboBox.setOnAction(e -> settingsChanged = true);
        
        // CheckBox监听器
        autoConnectLastCheck.setOnAction(e -> settingsChanged = true);
        minimizeToTrayCheck.setOnAction(e -> settingsChanged = true);
        checkUpdatesCheck.setOnAction(e -> settingsChanged = true);
        saveCommandHistoryCheck.setOnAction(e -> settingsChanged = true);
        autoScrollCheck.setOnAction(e -> settingsChanged = true);
        wordWrapCheck.setOnAction(e -> settingsChanged = true);
        strictHostKeyCheck.setOnAction(e -> settingsChanged = true);
        savePasswordCheck.setOnAction(e -> settingsChanged = true);
        overwriteFilesCheck.setOnAction(e -> settingsChanged = true);
        preserveTimestampCheck.setOnAction(e -> settingsChanged = true);
        showTransferProgressCheck.setOnAction(e -> settingsChanged = true);
        monitorCpuCheck.setOnAction(e -> settingsChanged = true);
        monitorMemoryCheck.setOnAction(e -> settingsChanged = true);
        monitorDiskCheck.setOnAction(e -> settingsChanged = true);
        monitorNetworkCheck.setOnAction(e -> settingsChanged = true);
        monitorProcessCheck.setOnAction(e -> settingsChanged = true);
        
        // TextField监听器
        downloadDirField.textProperty().addListener((obs, oldVal, newVal) -> settingsChanged = true);
    }

    /**
     * 加载设置到UI
     */
    private void loadSettings() {
        // 常规设置
        themeComboBox.setValue(settings.getProperty("theme", "系统默认"));
        languageComboBox.setValue(settings.getProperty("language", "简体中文"));
        autoConnectLastCheck.setSelected(Boolean.parseBoolean(settings.getProperty("autoConnectLast", "false")));
        minimizeToTrayCheck.setSelected(Boolean.parseBoolean(settings.getProperty("minimizeToTray", "false")));
        checkUpdatesCheck.setSelected(Boolean.parseBoolean(settings.getProperty("checkUpdates", "true")));
        
        // 终端设置
        fontFamilyComboBox.setValue(settings.getProperty("fontFamily", "Consolas"));
        double fontSize = Double.parseDouble(settings.getProperty("fontSize", "12"));
        fontSizeSlider.setValue(fontSize);
        fontSizeLabel.setText((int)fontSize + "px");
        colorSchemeComboBox.setValue(settings.getProperty("colorScheme", "默认"));
        saveCommandHistoryCheck.setSelected(Boolean.parseBoolean(settings.getProperty("saveCommandHistory", "true")));
        historyLimitSpinner.getValueFactory().setValue(Integer.parseInt(settings.getProperty("historyLimit", "100")));
        autoScrollCheck.setSelected(Boolean.parseBoolean(settings.getProperty("autoScroll", "true")));
        wordWrapCheck.setSelected(Boolean.parseBoolean(settings.getProperty("wordWrap", "true")));
        
        // 连接设置
        connectionTimeoutSpinner.getValueFactory().setValue(Integer.parseInt(settings.getProperty("connectionTimeout", "30")));
        readTimeoutSpinner.getValueFactory().setValue(Integer.parseInt(settings.getProperty("readTimeout", "60")));
        compressionCheck.setSelected(Boolean.parseBoolean(settings.getProperty("compression", "false")));
        compressionLevelSpinner.getValueFactory().setValue(Integer.parseInt(settings.getProperty("compressionLevel", "6")));
        compressionLevelSpinner.setDisable(!compressionCheck.isSelected());
        keepAliveCheck.setSelected(Boolean.parseBoolean(settings.getProperty("keepAlive", "true")));
        keepAliveIntervalSpinner.getValueFactory().setValue(Integer.parseInt(settings.getProperty("keepAliveInterval", "60")));
        keepAliveIntervalSpinner.setDisable(!keepAliveCheck.isSelected());
        strictHostKeyCheck.setSelected(Boolean.parseBoolean(settings.getProperty("strictHostKeyCheck", "false")));
        savePasswordCheck.setSelected(Boolean.parseBoolean(settings.getProperty("savePassword", "false")));
        autoCloseCheck.setSelected(Boolean.parseBoolean(settings.getProperty("autoClose", "false")));
        idleTimeoutSpinner.getValueFactory().setValue(Integer.parseInt(settings.getProperty("idleTimeout", "30")));
        idleTimeoutSpinner.setDisable(!autoCloseCheck.isSelected());
        
        // 文件传输设置
        downloadDirField.setText(settings.getProperty("downloadDir", System.getProperty("user.home") + "/Downloads"));
        overwriteFilesCheck.setSelected(Boolean.parseBoolean(settings.getProperty("overwriteFiles", "false")));
        preserveTimestampCheck.setSelected(Boolean.parseBoolean(settings.getProperty("preserveTimestamp", "true")));
        showTransferProgressCheck.setSelected(Boolean.parseBoolean(settings.getProperty("showTransferProgress", "true")));
        concurrentTransfersSpinner.getValueFactory().setValue(Integer.parseInt(settings.getProperty("concurrentTransfers", "3")));
        bufferSizeComboBox.setValue(settings.getProperty("bufferSize", "128KB"));
        
        // 监控设置
        monitoringIntervalSpinner.getValueFactory().setValue(Integer.parseInt(settings.getProperty("monitoringInterval", "2")));
        monitorCpuCheck.setSelected(Boolean.parseBoolean(settings.getProperty("monitorCpu", "true")));
        monitorMemoryCheck.setSelected(Boolean.parseBoolean(settings.getProperty("monitorMemory", "true")));
        monitorDiskCheck.setSelected(Boolean.parseBoolean(settings.getProperty("monitorDisk", "true")));
        monitorNetworkCheck.setSelected(Boolean.parseBoolean(settings.getProperty("monitorNetwork", "true")));
        monitorProcessCheck.setSelected(Boolean.parseBoolean(settings.getProperty("monitorProcess", "true")));
        enableAlertsCheck.setSelected(Boolean.parseBoolean(settings.getProperty("enableAlerts", "false")));
        cpuAlertThresholdSpinner.getValueFactory().setValue(Integer.parseInt(settings.getProperty("cpuAlertThreshold", "80")));
        memoryAlertThresholdSpinner.getValueFactory().setValue(Integer.parseInt(settings.getProperty("memoryAlertThreshold", "85")));
        diskAlertThresholdSpinner.getValueFactory().setValue(Integer.parseInt(settings.getProperty("diskAlertThreshold", "90")));
        
        boolean alertsEnabled = enableAlertsCheck.isSelected();
        cpuAlertThresholdSpinner.setDisable(!alertsEnabled);
        memoryAlertThresholdSpinner.setDisable(!alertsEnabled);
        diskAlertThresholdSpinner.setDisable(!alertsEnabled);
        
        settingsChanged = false;
    }

    /**
     * 保存设置
     */
    private void saveSettings() {
        // 常规设置
        settings.setProperty("theme", themeComboBox.getValue());
        settings.setProperty("language", languageComboBox.getValue());
        settings.setProperty("autoConnectLast", String.valueOf(autoConnectLastCheck.isSelected()));
        settings.setProperty("minimizeToTray", String.valueOf(minimizeToTrayCheck.isSelected()));
        settings.setProperty("checkUpdates", String.valueOf(checkUpdatesCheck.isSelected()));
        
        // 终端设置
        settings.setProperty("fontFamily", fontFamilyComboBox.getValue());
        settings.setProperty("fontSize", String.valueOf((int)fontSizeSlider.getValue()));
        settings.setProperty("colorScheme", colorSchemeComboBox.getValue());
        settings.setProperty("saveCommandHistory", String.valueOf(saveCommandHistoryCheck.isSelected()));
        settings.setProperty("historyLimit", String.valueOf(historyLimitSpinner.getValue()));
        settings.setProperty("autoScroll", String.valueOf(autoScrollCheck.isSelected()));
        settings.setProperty("wordWrap", String.valueOf(wordWrapCheck.isSelected()));
        
        // 连接设置
        settings.setProperty("connectionTimeout", String.valueOf(connectionTimeoutSpinner.getValue()));
        settings.setProperty("readTimeout", String.valueOf(readTimeoutSpinner.getValue()));
        settings.setProperty("compression", String.valueOf(compressionCheck.isSelected()));
        settings.setProperty("compressionLevel", String.valueOf(compressionLevelSpinner.getValue()));
        settings.setProperty("keepAlive", String.valueOf(keepAliveCheck.isSelected()));
        settings.setProperty("keepAliveInterval", String.valueOf(keepAliveIntervalSpinner.getValue()));
        settings.setProperty("strictHostKeyCheck", String.valueOf(strictHostKeyCheck.isSelected()));
        settings.setProperty("savePassword", String.valueOf(savePasswordCheck.isSelected()));
        settings.setProperty("autoClose", String.valueOf(autoCloseCheck.isSelected()));
        settings.setProperty("idleTimeout", String.valueOf(idleTimeoutSpinner.getValue()));
        
        // 文件传输设置
        settings.setProperty("downloadDir", downloadDirField.getText());
        settings.setProperty("overwriteFiles", String.valueOf(overwriteFilesCheck.isSelected()));
        settings.setProperty("preserveTimestamp", String.valueOf(preserveTimestampCheck.isSelected()));
        settings.setProperty("showTransferProgress", String.valueOf(showTransferProgressCheck.isSelected()));
        settings.setProperty("concurrentTransfers", String.valueOf(concurrentTransfersSpinner.getValue()));
        settings.setProperty("bufferSize", bufferSizeComboBox.getValue());
        
        // 监控设置
        settings.setProperty("monitoringInterval", String.valueOf(monitoringIntervalSpinner.getValue()));
        settings.setProperty("monitorCpu", String.valueOf(monitorCpuCheck.isSelected()));
        settings.setProperty("monitorMemory", String.valueOf(monitorMemoryCheck.isSelected()));
        settings.setProperty("monitorDisk", String.valueOf(monitorDiskCheck.isSelected()));
        settings.setProperty("monitorNetwork", String.valueOf(monitorNetworkCheck.isSelected()));
        settings.setProperty("monitorProcess", String.valueOf(monitorProcessCheck.isSelected()));
        settings.setProperty("enableAlerts", String.valueOf(enableAlertsCheck.isSelected()));
        settings.setProperty("cpuAlertThreshold", String.valueOf(cpuAlertThresholdSpinner.getValue()));
        settings.setProperty("memoryAlertThreshold", String.valueOf(memoryAlertThresholdSpinner.getValue()));
        settings.setProperty("diskAlertThreshold", String.valueOf(diskAlertThresholdSpinner.getValue()));
        
        // 保存到文件
        historyManager.saveSettings(settings);
        settingsChanged = false;
        
        logger.info("Settings saved successfully");
    }

    /**
     * 浏览下载目录
     */
    @FXML
    private void browseDownloadDir() {
        DirectoryChooser chooser = new DirectoryChooser();
        chooser.setTitle("选择下载目录");
        
        File currentDir = new File(downloadDirField.getText());
        if (currentDir.exists() && currentDir.isDirectory()) {
            chooser.setInitialDirectory(currentDir);
        }
        
        Stage stage = (Stage) downloadDirField.getScene().getWindow();
        File selectedDir = chooser.showDialog(stage);
        
        if (selectedDir != null) {
            downloadDirField.setText(selectedDir.getAbsolutePath());
            settingsChanged = true;
        }
    }

    /**
     * 恢复默认设置
     */
    @FXML
    private void restoreDefaults() {
        Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
        alert.setTitle("确认");
        alert.setHeaderText("恢复默认设置");
        alert.setContentText("这将重置所有设置为默认值，是否继续？");
        
        if (alert.showAndWait().orElse(ButtonType.CANCEL) == ButtonType.OK) {
            settings.clear();
            loadSettings();
            settingsChanged = true;
        }
    }

    /**
     * 应用设置
     */
    @FXML
    private void applySettings() {
        saveSettings();
        showAlert("信息", "设置已应用");
    }

    /**
     * 保存并关闭
     */
    @FXML
    private void saveAndClose() {
        saveSettings();
        closeWindow();
    }

    /**
     * 取消
     */
    @FXML
    private void cancel() {
        if (settingsChanged) {
            Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
            alert.setTitle("确认");
            alert.setHeaderText("未保存的更改");
            alert.setContentText("设置已更改但未保存，是否放弃更改？");
            
            if (alert.showAndWait().orElse(ButtonType.CANCEL) != ButtonType.OK) {
                return;
            }
        }
        closeWindow();
    }

    /**
     * 关闭窗口
     */
    private void closeWindow() {
        Stage stage = (Stage) themeComboBox.getScene().getWindow();
        stage.close();
    }

    /**
     * 显示提示对话框
     */
    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    /**
     * 获取设置属性
     */
    public Properties getSettings() {
        return settings;
    }
}
