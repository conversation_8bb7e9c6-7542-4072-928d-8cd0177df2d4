package com.sshtool;

import com.sshtool.service.ConnectionManager;
import javafx.application.Platform;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.TextArea;
import javafx.scene.input.KeyCode;
import javafx.scene.input.KeyEvent;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.util.ResourceBundle;

/**
 * 终端标签页控制器类，管理SSH终端的输入输出和命令执行
 */
public class TerminalTabController implements Initializable {

    @FXML
    private TextArea terminalTextArea;
    
    private ConnectionManager connectionManager;
    private String currentPrompt = "> ";
    private String commandBuffer = "";
    private boolean isReadyForInput = true;
    private OutputStream shellOutput;
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        // 设置文本区域的初始状态
        terminalTextArea.setEditable(true);
        terminalTextArea.setWrapText(true);
        terminalTextArea.setPromptText("请输入命令...");
        
        // 添加键盘事件监听
        terminalTextArea.setOnKeyPressed(event -> {
            handleKeyPress(event);
        });
        
        // 添加焦点事件监听
        terminalTextArea.focusedProperty().addListener((observable, oldValue, newValue) -> {
            if (newValue && isReadyForInput) {
                // 当获得焦点时，将光标移到最后
                Platform.runLater(() -> {
                    terminalTextArea.positionCaret(terminalTextArea.getText().length());
                });
            }
        });
    }
    
    /**
     * 设置连接管理器
     * @param connectionManager 连接管理器实例
     */
    public void setConnectionManager(ConnectionManager connectionManager) {
        this.connectionManager = connectionManager;
        if (connectionManager != null && connectionManager.isConnected()) {
            initializeTerminal();
        }
    }
    
    /**
     * 初始化终端
     */
    private void initializeTerminal() {
        try {
            // 获取Shell的输入输出流
            shellOutput = connectionManager.getShellOutput();
            InputStream shellInput = connectionManager.getShellInput();
            
            if (shellInput != null) {
                // 创建一个线程来读取服务器输出
                new Thread(() -> {
                    byte[] buffer = new byte[1024];
                    int bytesRead;
                    try {
                        while ((bytesRead = shellInput.read(buffer)) != -1) {
                            String output = new String(buffer, 0, bytesRead);
                            Platform.runLater(() -> {
                                appendToTerminal(output);
                            });
                        }
                    } catch (IOException e) {
                        if (!e.getMessage().contains("Stream closed")) {
                            System.err.println("Error reading from shell: " + e.getMessage());
                        }
                    }
                }).start();
            }
            
            // 显示初始提示符
            appendToTerminal(currentPrompt);
            isReadyForInput = true;
        } catch (Exception e) {
            System.err.println("Failed to initialize terminal: " + e.getMessage());
            appendToTerminal("\nFailed to initialize terminal: " + e.getMessage() + "\n");
        }
    }
    
    /**
     * 处理键盘按键事件
     * @param event 键盘事件
     */
    private void handleKeyPress(KeyEvent event) {
        if (!isReadyForInput) {
            event.consume();
            return;
        }
        
        if (event.getCode() == KeyCode.ENTER) {
            // 处理回车键，执行命令
            executeCommand();
            event.consume();
        } else if (event.getCode() == KeyCode.BACK_SPACE) {
            // 处理退格键，确保不能删除提示符
            String text = terminalTextArea.getText();
            int caretPosition = terminalTextArea.getCaretPosition();
            int lastPromptIndex = text.lastIndexOf(currentPrompt);
            
            if (lastPromptIndex != -1 && caretPosition <= lastPromptIndex + currentPrompt.length()) {
                event.consume();
            }
        } else if (event.getCode() == KeyCode.LEFT) {
            // 处理左箭头键，确保不能移动到提示符之前
            int caretPosition = terminalTextArea.getCaretPosition();
            String text = terminalTextArea.getText();
            int lastPromptIndex = text.lastIndexOf(currentPrompt);
            
            if (lastPromptIndex != -1 && caretPosition <= lastPromptIndex + currentPrompt.length()) {
                event.consume();
            }
        } else if (event.getCode() == KeyCode.UP || event.getCode() == KeyCode.DOWN) {
            // 处理上下箭头键，可以用于命令历史（此处未实现）
            event.consume();
        }
        // 其他按键让JavaFX默认处理
    }
    
    /**
     * 执行命令
     */
    private void executeCommand() {
        String text = terminalTextArea.getText();
        int lastPromptIndex = text.lastIndexOf(currentPrompt);
        
        if (lastPromptIndex != -1) {
            // 提取命令
            commandBuffer = text.substring(lastPromptIndex + currentPrompt.length()).trim();
            
            // 发送命令到服务器
            if (!commandBuffer.isEmpty() && shellOutput != null && connectionManager != null && connectionManager.isConnected()) {
                try {
                    shellOutput.write((commandBuffer + "\n").getBytes());
                    shellOutput.flush();
                } catch (IOException e) {
                    System.err.println("Error sending command: " + e.getMessage());
                    appendToTerminal("\nError sending command: " + e.getMessage() + "\n");
                }
            }
        }
        
        // 重置命令缓冲区
        commandBuffer = "";
        
        // 移动光标到新行
        appendToTerminal("");
    }
    
    /**
     * 向终端添加文本
     * @param text 要添加的文本
     */
    public void appendToTerminal(String text) {
        terminalTextArea.appendText(text);
        terminalTextArea.positionCaret(terminalTextArea.getText().length());
    }
    
    /**
     * 设置当前提示符
     * @param prompt 提示符字符串
     */
    public void setCurrentPrompt(String prompt) {
        this.currentPrompt = prompt;
    }
    
    /**
     * 发送命令到服务器
     * @param command 要发送的命令
     */
    public void sendCommand(String command) {
        if (shellOutput != null && connectionManager != null && connectionManager.isConnected()) {
            try {
                shellOutput.write((command + "\n").getBytes());
                shellOutput.flush();
            } catch (IOException e) {
                System.err.println("Error sending command: " + e.getMessage());
                appendToTerminal("\nError sending command: " + e.getMessage() + "\n");
            }
        }
    }
    
    /**
     * 清除终端内容
     */
    public void clearTerminal() {
        terminalTextArea.clear();
        appendToTerminal(currentPrompt);
    }
    
    /**
     * 关闭终端
     */
    public void closeTerminal() {
        isReadyForInput = false;
        // 可以在这里释放资源
    }
}