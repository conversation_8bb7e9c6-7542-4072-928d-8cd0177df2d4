package com.sshtool;

import com.jcraft.jsch.*;
import com.sshtool.model.ConnectionInfo;
import com.sshtool.model.ServerStats;
import com.sshtool.service.ConnectionManager;
import com.sshtool.service.FileManager;
import com.sshtool.service.HistoryManager;
import com.sshtool.service.MonitoringService;
import javafx.application.Platform;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.geometry.Insets;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.input.KeyCode;
import javafx.scene.layout.*;
import javafx.stage.FileChooser;
import javafx.stage.Modality;
import javafx.stage.Stage;

import java.io.*;
import java.net.URL;
import java.util.List;
import java.util.ResourceBundle;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 主控制器类，管理SSH客户端的主要功能
 */
public class MainController implements Initializable {

    @FXML
    private TabPane mainTabPane;

    @FXML
    private Tab consoleTab;
    @FXML
    private Tab fileManagerTab;
    @FXML
    private Tab monitoringTab;
    @FXML
    private Tab connectionTab;

    @FXML
    private TextArea consoleOutput;
    @FXML
    private TextField commandInput;
    @FXML
    private ComboBox<ConnectionInfo> savedConnections;

    @FXML
    private TextField hostnameField;
    @FXML
    private TextField portField;
    @FXML
    private TextField usernameField;
    @FXML
    private PasswordField passwordField;
    @FXML
    private TextField privateKeyField;
    @FXML
    private PasswordField passphraseField;
    @FXML
    private CheckBox saveConnectionCheck;

    @FXML
    private TreeView<String> fileTree;
    @FXML
    private ListView<String> fileList;

    @FXML
    private ProgressBar cpuUsageBar;
    @FXML
    private ProgressBar memoryUsageBar;
    @FXML
    private ProgressBar diskUsageBar;
    @FXML
    private Label cpuUsageLabel;
    @FXML
    private Label memoryUsageLabel;
    @FXML
    private Label diskUsageLabel;
    @FXML
    private Label networkRxLabel;
    @FXML
    private Label networkTxLabel;

    private ConnectionManager connectionManager;
    private FileManager fileManager;
    private HistoryManager historyManager;
    private MonitoringService monitoringService;
    private ExecutorService executorService;

    private ObservableList<ConnectionInfo> connectionList;
    private Session currentSession;
    private ChannelShell currentChannel;

    @Override
    public void initialize(URL url, ResourceBundle resourceBundle) {
        // 初始化服务
        connectionManager = new ConnectionManager();
        fileManager = new FileManager();
        historyManager = new HistoryManager();
        monitoringService = new MonitoringService();
        executorService = Executors.newCachedThreadPool();

        // 加载保存的连接信息
        loadSavedConnections();

        // 设置命令输入框的回车事件
        commandInput.setOnKeyPressed(event -> {
            if (event.getCode() == KeyCode.ENTER) {
                executeCommand();
            }
        });

        // 设置保存的连接下拉框的选择事件
        savedConnections.setOnAction(event -> {
            ConnectionInfo info = savedConnections.getValue();
            if (info != null) {
                hostnameField.setText(info.getHostname());
                portField.setText(String.valueOf(info.getPort()));
                usernameField.setText(info.getUsername());
                passwordField.setText(info.getPassword());
                privateKeyField.setText(info.getPrivateKeyPath());
                passphraseField.setText(info.getPassphrase());
            }
        });

        // 初始状态下禁用某些组件
        disableComponents(true);
    }

    /**
     * 连接按钮点击事件
     */
    @FXML
    private void connect() {
        String hostname = hostnameField.getText();
        int port = Integer.parseInt(portField.getText());
        String username = usernameField.getText();
        String password = passwordField.getText();
        String privateKeyPath = privateKeyField.getText();
        String passphrase = passphraseField.getText();

        // 保存连接信息
        if (saveConnectionCheck.isSelected()) {
            ConnectionInfo info = new ConnectionInfo(hostname, port, username, password, privateKeyPath, passphrase);
            historyManager.saveConnection(info);
            loadSavedConnections();
        }

        // 尝试连接
        executorService.submit(() -> {
            try {
                connectToServer(hostname, port, username, password, privateKeyPath, passphrase);
            } catch (Exception e) {
                Platform.runLater(() -> {
                    appendToConsole("Error: " + e.getMessage() + "\n");
                    showAlert(Alert.AlertType.ERROR, "Connection Error", e.getMessage());
                });
            }
        });
    }
    
    /**
     * 连接到服务器的实际操作
     */
    private void connectToServer(String hostname, int port, String username, String password, 
                                String privateKeyPath, String passphrase) throws Exception {
        currentSession = connectionManager.connect(hostname, port, username, password, privateKeyPath, passphrase);
        currentChannel = (ChannelShell) connectionManager.openShellChannel(currentSession);

        // 启动通道
        currentChannel.connect();
        
        // 设置输入输出流
        connectionManager.setupShellStreams(currentChannel);
        
        // 获取输入流
        InputStream is = currentChannel.getInputStream();
        
        // 更新UI
        Platform.runLater(() -> {
            appendToConsole("Connected to " + hostname + ":" + port + " as " + username + "\n");
            disableComponents(false);
            mainTabPane.getSelectionModel().select(consoleTab);

            // 启动监控
            startMonitoring();
            // 加载文件系统
            loadFileSystem();
        });

        // 读取输出流
        readConsoleOutput(is);
    }

    /**
     * 断开连接按钮点击事件
     */
    @FXML
    private void disconnect() {
        if (currentChannel != null && currentChannel.isConnected()) {
            currentChannel.disconnect();
        }
        if (currentSession != null && currentSession.isConnected()) {
            currentSession.disconnect();
        }
        appendToConsole("Disconnected\n");
        disableComponents(true);
        monitoringService.stopMonitoring();
    }

    /**
     * 执行命令
     */
    private void executeCommand() {
        String command = commandInput.getText();
        if (command.isEmpty() || currentChannel == null || !currentChannel.isConnected()) {
            return;
        }

        appendToConsole("$ " + command + "\n");
        commandInput.clear();

        try {
            // 使用connectionManager获取输出流
            OutputStream os = connectionManager.getShellOutput();
            os.write((command + "\n").getBytes());
            os.flush();
        } catch (IOException e) {
            appendToConsole("Error executing command: " + e.getMessage() + "\n");
        }
    }

    /**
     * 读取控制台输出
     */
    private void readConsoleOutput(InputStream is) {
        byte[] buffer = new byte[1024];
        int bytesRead;
        try {
            while ((bytesRead = is.read(buffer)) != -1) {
                String output = new String(buffer, 0, bytesRead);
                appendToConsole(output);
            }
        } catch (IOException e) {
            if (currentChannel != null && currentChannel.isConnected()) {
                appendToConsole("Error reading output: " + e.getMessage() + "\n");
            }
        }
    }

    /**
     * 启动监控服务
     */
    private void startMonitoring() {
        monitoringService.startMonitoring(currentSession, stats -> {
            Platform.runLater(() -> {
                updateMonitoringStats(stats);
            });
        });
    }

    /**
     * 更新监控统计信息
     */
    private void updateMonitoringStats(ServerStats stats) {
        cpuUsageBar.setProgress(stats.getCpuUsage() / 100.0);
        memoryUsageBar.setProgress(stats.getMemoryUsage() / 100.0);
        diskUsageBar.setProgress(stats.getDiskUsage() / 100.0);
        
        cpuUsageLabel.setText(String.format("CPU: %.1f%%", stats.getCpuUsage()));
        memoryUsageLabel.setText(String.format("Memory: %.1f%%", stats.getMemoryUsage()));
        diskUsageLabel.setText(String.format("Disk: %.1f%%", stats.getDiskUsage()));
        networkRxLabel.setText(String.format("Rx: %.2f MB/s", stats.getNetworkRx()));
        networkTxLabel.setText(String.format("Tx: %.2f MB/s", stats.getNetworkTx()));
    }

    /**
     * 加载文件系统
     */
    private void loadFileSystem() {
        executorService.submit(() -> {
            try {
                List<String> rootDirectories = fileManager.getRootDirectories(currentSession);
                Platform.runLater(() -> {
                    // 清空当前的文件树
                    fileTree.setRoot(null);
                    
                    // 创建根节点
                    TreeItem<String> root = new TreeItem<>("/");
                    root.setExpanded(true);
                    
                    // 添加根目录
                    for (String dir : rootDirectories) {
                        TreeItem<String> dirItem = new TreeItem<>(dir);
                        dirItem.setExpanded(false);
                        
                        // 设置展开监听器，懒加载子目录
                        dirItem.expandedProperty().addListener((observable, oldValue, newValue) -> {
                            if (newValue && dirItem.getChildren().isEmpty()) {
                                loadDirectoryContent(dir, dirItem);
                            }
                        });
                        
                        root.getChildren().add(dirItem);
                    }
                    
                    fileTree.setRoot(root);
                });
            } catch (Exception e) {
                Platform.runLater(() -> {
                    showAlert(Alert.AlertType.ERROR, "File System Error", e.getMessage());
                });
            }
        });
    }

    /**
     * 加载目录内容
     */
    private void loadDirectoryContent(String path, TreeItem<String> parentItem) {
        executorService.submit(() -> {
            try {
                List<String> files = fileManager.listFiles(currentSession, path);
                Platform.runLater(() -> {
                    for (String file : files) {
                        TreeItem<String> fileItem = new TreeItem<>(file);
                        parentItem.getChildren().add(fileItem);
                    }
                });
            } catch (Exception e) {
                Platform.runLater(() -> {
                    showAlert(Alert.AlertType.ERROR, "File System Error", e.getMessage());
                });
            }
        });
    }

    /**
     * 下载文件按钮点击事件
     */
    @FXML
    private void downloadFile() {
        TreeItem<String> selectedItem = fileTree.getSelectionModel().getSelectedItem();
        if (selectedItem == null || currentSession == null || !currentSession.isConnected()) {
            return;
        }

        // 构建完整路径
        StringBuilder pathBuilder = new StringBuilder();
        TreeItem<String> current = selectedItem;
        while (current != fileTree.getRoot()) {
            pathBuilder.insert(0, current.getValue() + "/");
            current = current.getParent();
        }
        pathBuilder.insert(0, "/");
        String remoteFilePath = pathBuilder.toString();

        // 显示文件保存对话框
        FileChooser fileChooser = new FileChooser();
        fileChooser.setInitialFileName(selectedItem.getValue());
        File localFile = fileChooser.showSaveDialog(new Stage());

        if (localFile != null) {
            executorService.submit(() -> {
                try {
                    fileManager.downloadFile(currentSession, remoteFilePath, localFile.getAbsolutePath());
                    Platform.runLater(() -> {
                        showAlert(Alert.AlertType.INFORMATION, "Download Complete", 
                                "File downloaded successfully: " + localFile.getAbsolutePath());
                    });
                } catch (Exception e) {
                    Platform.runLater(() -> {
                        showAlert(Alert.AlertType.ERROR, "Download Error", e.getMessage());
                    });
                }
            });
        }
    }

    /**
     * 上传文件按钮点击事件
     */
    @FXML
    private void uploadFile() {
        TreeItem<String> selectedItem = fileTree.getSelectionModel().getSelectedItem();
        if (selectedItem == null || currentSession == null || !currentSession.isConnected()) {
            return;
        }

        // 构建完整路径
        StringBuilder pathBuilder = new StringBuilder();
        TreeItem<String> current = selectedItem;
        while (current != fileTree.getRoot()) {
            pathBuilder.insert(0, current.getValue() + "/");
            current = current.getParent();
        }
        pathBuilder.insert(0, "/");
        String remoteDirPath = pathBuilder.toString();

        // 显示文件选择对话框
        FileChooser fileChooser = new FileChooser();
        File localFile = fileChooser.showOpenDialog(new Stage());

        if (localFile != null) {
            executorService.submit(() -> {
                try {
                    String remoteFilePath = remoteDirPath + "/" + localFile.getName();
                    fileManager.uploadFile(currentSession, localFile.getAbsolutePath(), remoteFilePath);
                    Platform.runLater(() -> {
                        showAlert(Alert.AlertType.INFORMATION, "Upload Complete", 
                                "File uploaded successfully: " + remoteFilePath);
                        // 刷新文件列表
                        loadFileSystem();
                    });
                } catch (Exception e) {
                    Platform.runLater(() -> {
                        showAlert(Alert.AlertType.ERROR, "Upload Error", e.getMessage());
                    });
                }
            });
        }
    }

    /**
     * 加载保存的连接信息
     */
    private void loadSavedConnections() {
        List<ConnectionInfo> savedConnectionsList = historyManager.loadConnections();
        connectionList = FXCollections.observableArrayList(savedConnectionsList);
        savedConnections.setItems(connectionList);
    }

    /**
     * 禁用或启用组件
     */
    private void disableComponents(boolean disable) {
        consoleTab.setDisable(disable);
        fileManagerTab.setDisable(disable);
        monitoringTab.setDisable(disable);
        commandInput.setDisable(disable);
    }

    /**
     * 向控制台追加输出
     */
    private void appendToConsole(String text) {
        Platform.runLater(() -> {
            consoleOutput.appendText(text);
            // 自动滚动到底部
            consoleOutput.setScrollTop(Double.MAX_VALUE);
        });
    }

    /**
     * 显示警告对话框
     */
    private void showAlert(Alert.AlertType alertType, String title, String message) {
        Alert alert = new Alert(alertType);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    /**
     * 打开新的终端标签页
     */
    @FXML
    private void openNewTerminal() {
        if (currentSession == null || !currentSession.isConnected()) {
            showAlert(Alert.AlertType.WARNING, "Not Connected", "Please connect to a server first");
            return;
        }

        try {
            // 创建新的终端标签
            Tab newTab = new Tab("Terminal " + (mainTabPane.getTabs().size() - 3));
            
            // 加载终端FXML文件
            FXMLLoader loader = new FXMLLoader(getClass().getResource("terminal-tab.fxml"));
            Parent terminalContent = loader.load();
            TerminalTabController terminalController = loader.getController();
            
            // 设置内容到标签页
            newTab.setContent(terminalContent);
            
            // 添加到标签页
            mainTabPane.getTabs().add(1, newTab);
            mainTabPane.getSelectionModel().select(newTab);
            
            // 设置连接管理器
            terminalController.setConnectionManager(connectionManager);
            
            // 设置标签关闭事件
            newTab.setOnCloseRequest(event -> {
                terminalController.closeTerminal();
            });

        } catch (IOException e) {
            showAlert(Alert.AlertType.ERROR, "Terminal Error", e.getMessage());
        }
    }

    /**
     * 打开设置对话框
     */
    @FXML
    private void openSettings() {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("settings-view.fxml"));
            Parent root = loader.load();
            Stage stage = new Stage();
            stage.setTitle("Settings");
            stage.setScene(new Scene(root));
            stage.initModality(Modality.APPLICATION_MODAL);
            stage.showAndWait();
        } catch (IOException e) {
            showAlert(Alert.AlertType.ERROR, "Settings Error", e.getMessage());
        }
    }
}