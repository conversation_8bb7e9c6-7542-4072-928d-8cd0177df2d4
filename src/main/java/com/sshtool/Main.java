package com.sshtool;

import javafx.application.Application;
import javafx.fxml.FXMLLoader;
import javafx.scene.Scene;
import javafx.scene.image.Image;
import javafx.stage.Stage;

import java.io.IOException;
import java.util.Objects;

/**
 * 主应用类，启动JavaFX SSH客户端
 */
public class Main extends Application {

    /**
     * 启动JavaFX应用
     * @param stage 主舞台
     * @throws IOException 加载FXML文件时可能发生的异常
     */
    @Override
    public void start(Stage stage) throws IOException {
        // 加载主界面FXML文件
        FXMLLoader fxmlLoader = new FXMLLoader(Main.class.getResource("main-view.fxml"));
        Scene scene = new Scene(fxmlLoader.load(), 1200, 800);
        
        // 设置应用标题
        stage.setTitle("Java SSH Client");
        
        // 设置场景并显示舞台
        stage.setScene(scene);
        stage.show();
    }

    /**
     * 主入口方法
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        launch();
    }
}