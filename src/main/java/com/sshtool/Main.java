package com.sshtool;

import javafx.application.Application;
import javafx.application.Platform;
import javafx.fxml.FXMLLoader;
import javafx.scene.Scene;
import javafx.scene.image.Image;
import javafx.stage.Stage;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.IOException;
import java.util.Objects;

/**
 * 主应用类，启动JavaFX SSH客户端
 */
public class Main extends Application {
    private static final Logger logger = LogManager.getLogger(Main.class);
    
    private static final String APP_TITLE = "Java SSH Client";
    private static final String VERSION = "1.0.0";
    private static final int DEFAULT_WIDTH = 1200;
    private static final int DEFAULT_HEIGHT = 800;
    private static final int MIN_WIDTH = 800;
    private static final int MIN_HEIGHT = 600;

    /**
     * 启动JavaFX应用
     * @param stage 主舞台
     * @throws IOException 加载FXML文件时可能发生的异常
     */
    @Override
    public void start(Stage stage) throws IOException {
        logger.info("Starting {} v{}", APP_TITLE, VERSION);
        
        try {
            // 加载主界面FXML文件
            FXMLLoader fxmlLoader = new FXMLLoader(Main.class.getResource("/com/sshtool/main-view.fxml"));
            Scene scene = new Scene(fxmlLoader.load(), DEFAULT_WIDTH, DEFAULT_HEIGHT);
            
            // 加载CSS样式
            String cssPath = Objects.requireNonNull(Main.class.getResource("/css/modern-theme.css")).toExternalForm();
            scene.getStylesheets().add(cssPath);
            
            // 设置应用图标
            try {
                Image icon = new Image(Objects.requireNonNull(Main.class.getResourceAsStream("/images/app-icon.png")));
                stage.getIcons().add(icon);
            } catch (Exception e) {
                logger.warn("Could not load application icon: {}", e.getMessage());
            }
            
            // 配置主窗口
            stage.setTitle(APP_TITLE + " v" + VERSION);
            stage.setScene(scene);
            stage.setMinWidth(MIN_WIDTH);
            stage.setMinHeight(MIN_HEIGHT);
            
            // 设置窗口关闭事件
            stage.setOnCloseRequest(event -> {
                logger.info("Application closing...");
                // 这里可以添加清理逻辑，比如断开SSH连接等
                Platform.exit();
                System.exit(0);
            });
            
            // 显示窗口
            stage.show();
            
            logger.info("Application started successfully");
            
        } catch (Exception e) {
            logger.error("Failed to start application", e);
            showErrorAndExit("启动失败", "无法启动应用程序: " + e.getMessage());
        }
    }

    /**
     * 应用程序初始化
     */
    @Override
    public void init() throws Exception {
        super.init();
        logger.info("Initializing application...");
        
        // 设置系统属性
        System.setProperty("javafx.preloader", "com.sshtool.SplashScreenPreloader");
        
        // 设置日志配置
        System.setProperty("log4j.configurationFile", "log4j2.xml");
        
        // 检查JavaFX运行时
        try {
            Class.forName("javafx.application.Application");
        } catch (ClassNotFoundException e) {
            logger.error("JavaFX runtime not found");
            throw new RuntimeException("JavaFX runtime is required to run this application", e);
        }
        
        logger.info("Application initialized successfully");
    }

    /**
     * 应用程序停止
     */
    @Override
    public void stop() throws Exception {
        logger.info("Stopping application...");
        
        // 清理资源
        // 这里可以添加清理逻辑，比如：
        // - 断开所有SSH连接
        // - 保存应用状态
        // - 清理临时文件等
        
        super.stop();
        logger.info("Application stopped");
    }

    /**
     * 显示错误信息并退出
     */
    private void showErrorAndExit(String title, String message) {
        Platform.runLater(() -> {
            javafx.scene.control.Alert alert = new javafx.scene.control.Alert(
                javafx.scene.control.Alert.AlertType.ERROR);
            alert.setTitle(title);
            alert.setHeaderText(null);
            alert.setContentText(message);
            alert.showAndWait();
            Platform.exit();
            System.exit(1);
        });
    }

    /**
     * 主入口方法
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        logger.info("Starting Java SSH Client...");
        
        // 检查Java版本
        String javaVersion = System.getProperty("java.version");
        logger.info("Java version: {}", javaVersion);
        
        if (!isJavaVersionSupported()) {
            System.err.println("Error: Java 17 or higher is required to run this application.");
            System.err.println("Current Java version: " + javaVersion);
            System.exit(1);
        }
        
        // 设置系统属性
        setupSystemProperties();
        
        // 处理命令行参数
        processCommandLineArgs(args);
        
        try {
            // 启动JavaFX应用
            launch(args);
        } catch (Exception e) {
            logger.error("Failed to launch application", e);
            System.err.println("Failed to start application: " + e.getMessage());
            System.exit(1);
        }
    }

    /**
     * 检查Java版本是否支持
     */
    private static boolean isJavaVersionSupported() {
        String version = System.getProperty("java.version");
        try {
            // 解析版本号
            String[] parts = version.split("\\.");
            int majorVersion;
            
            if (parts[0].equals("1")) {
                // Java 8及以下版本格式：1.8.0_xxx
                majorVersion = Integer.parseInt(parts[1]);
            } else {
                // Java 9及以上版本格式：17.0.1
                majorVersion = Integer.parseInt(parts[0]);
            }
            
            return majorVersion >= 17;
        } catch (Exception e) {
            logger.warn("Could not parse Java version: {}", version);
            return false;
        }
    }

    /**
     * 设置系统属性
     */
    private static void setupSystemProperties() {
        // 设置应用程序名称
        System.setProperty("app.name", APP_TITLE);
        System.setProperty("app.version", VERSION);
        
        // 设置JavaFX属性
        System.setProperty("javafx.animation.fullspeed", "true");
        System.setProperty("prism.lcdtext", "false");
        System.setProperty("prism.text", "t2k");
        
        // 在高DPI显示器上的设置
        System.setProperty("glass.win.uiScale", "100%");
        System.setProperty("glass.gtk.uiScale", "1.0");
        
        // 设置文件编码
        System.setProperty("file.encoding", "UTF-8");
        
        logger.info("System properties configured");
    }

    /**
     * 处理命令行参数
     */
    private static void processCommandLineArgs(String[] args) {
        for (int i = 0; i < args.length; i++) {
            String arg = args[i];
            
            switch (arg) {
                case "--version":
                case "-v":
                    System.out.println(APP_TITLE + " version " + VERSION);
                    System.exit(0);
                    break;
                    
                case "--help":
                case "-h":
                    printHelp();
                    System.exit(0);
                    break;
                    
                case "--debug":
                    System.setProperty("app.debug", "true");
                    System.setProperty("log4j2.level", "DEBUG");
                    logger.info("Debug mode enabled");
                    break;
                    
                case "--config":
                    if (i + 1 < args.length) {
                        System.setProperty("app.config.dir", args[++i]);
                        logger.info("Custom config directory: {}", args[i]);
                    } else {
                        System.err.println("Error: --config requires a directory path");
                        System.exit(1);
                    }
                    break;
                    
                default:
                    if (arg.startsWith("-")) {
                        System.err.println("Unknown option: " + arg);
                        printHelp();
                        System.exit(1);
                    }
                    break;
            }
        }
    }

    /**
     * 打印帮助信息
     */
    private static void printHelp() {
        System.out.println(APP_TITLE + " v" + VERSION);
        System.out.println("A modern SSH client built with JavaFX");
        System.out.println();
        System.out.println("Usage: java -jar java-ssh-client.jar [options]");
        System.out.println();
        System.out.println("Options:");
        System.out.println("  -h, --help           Show this help message");
        System.out.println("  -v, --version        Show version information");
        System.out.println("  --debug              Enable debug mode");
        System.out.println("  --config <dir>       Use custom configuration directory");
        System.out.println();
        System.out.println("Features:");
        System.out.println("  • Modern and intuitive user interface");
        System.out.println("  • SSH connection management with saved profiles");
        System.out.println("  • Interactive terminal with command history");
        System.out.println("  • File manager with upload/download capabilities");
        System.out.println("  • Real-time server monitoring (CPU, Memory, Disk, Network)");
        System.out.println("  • Customizable settings and themes");
        System.out.println();
        System.out.println("Requirements:");
        System.out.println("  • Java 17 or higher");
        System.out.println("  • JavaFX 19 or higher");
        System.out.println();
        System.out.println("For more information, visit: https://github.com/your-repo/java-ssh-client");
    }
}
