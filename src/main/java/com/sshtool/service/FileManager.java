package com.sshtool.service;

import com.jcraft.jsch.*;
import com.sshtool.model.FileItem;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.*;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 文件管理器，负责远程文件系统的操作
 */
public class FileManager {
    private static final Logger logger = LogManager.getLogger(FileManager.class);
    private ConnectionManager connectionManager;
    private static final Pattern LS_PATTERN = Pattern.compile(
        "^([drwx-]{10})\\s+(\\d+)\\s+(\\w+)\\s+(\\w+)\\s+(\\d+)\\s+(\\w{3}\\s+\\d{1,2}\\s+(?:\\d{4}|\\d{2}:\\d{2}))\\s+(.+)$"
    );
    private static final SimpleDateFormat DATE_FORMAT_WITH_YEAR = new SimpleDateFormat("MMM dd yyyy", Locale.ENGLISH);
    private static final SimpleDateFormat DATE_FORMAT_WITH_TIME = new SimpleDateFormat("MMM dd HH:mm", Locale.ENGLISH);
    
    /**
     * 构造函数
     */
    public FileManager() {
        this.connectionManager = new ConnectionManager();
    }
    
    /**
     * 列出目录内容
     * @param session SSH会话
     * @param path 目录路径
     * @return 文件列表
     * @throws Exception 操作异常
     */
    public List<FileItem> listDirectory(Session session, String path) throws Exception {
        logger.info("Listing directory: {}", path);
        
        String command = "ls -la " + escapePath(path);
        String output = connectionManager.executeCommand(session, command);
        
        return parseDirectoryListing(output, path);
    }
    
    /**
     * 解析ls命令输出
     * @param output ls命令输出
     * @param basePath 基础路径
     * @return 文件列表
     */
    private List<FileItem> parseDirectoryListing(String output, String basePath) {
        List<FileItem> files = new ArrayList<>();
        String[] lines = output.split("\n");
        
        for (String line : lines) {
            line = line.trim();
            if (line.isEmpty() || line.startsWith("total")) {
                continue;
            }
            
            Matcher matcher = LS_PATTERN.matcher(line);
            if (matcher.matches()) {
                try {
                    String permissions = matcher.group(1);
                    String owner = matcher.group(3);
                    String group = matcher.group(4);
                    long size = Long.parseLong(matcher.group(5));
                    String dateStr = matcher.group(6);
                    String name = matcher.group(7);
                    
                    // 跳过当前目录和父目录
                    if (".".equals(name) || "..".equals(name)) {
                        continue;
                    }
                    
                    boolean isDirectory = permissions.startsWith("d");
                    boolean isSymlink = permissions.startsWith("l");
                    
                    // 处理符号链接
                    String linkTarget = "";
                    if (isSymlink && name.contains(" -> ")) {
                        String[] parts = name.split(" -> ", 2);
                        name = parts[0];
                        linkTarget = parts[1];
                    }
                    
                    // 解析日期
                    Date lastModified = parseDate(dateStr);
                    
                    // 构建完整路径
                    String fullPath = basePath.endsWith("/") ? basePath + name : basePath + "/" + name;
                    
                    FileItem fileItem = new FileItem(name, fullPath, isDirectory, size, lastModified, permissions, owner, group);
                    fileItem.setSymlink(isSymlink);
                    fileItem.setLinkTarget(linkTarget);
                    
                    files.add(fileItem);
                    
                } catch (Exception e) {
                    logger.warn("Failed to parse line: {}", line, e);
                }
            }
        }
        
        // 排序：目录在前，然后按名称排序
        files.sort((a, b) -> {
            if (a.isDirectory() != b.isDirectory()) {
                return a.isDirectory() ? -1 : 1;
            }
            return a.getName().compareToIgnoreCase(b.getName());
        });
        
        return files;
    }
    
    /**
     * 解析日期字符串
     * @param dateStr 日期字符串
     * @return Date对象
     */
    private Date parseDate(String dateStr) {
        try {
            if (dateStr.contains(":")) {
                // 包含时间，表示今年的文件
                Date date = DATE_FORMAT_WITH_TIME.parse(dateStr);
                Calendar cal = Calendar.getInstance();
                cal.setTime(date);
                cal.set(Calendar.YEAR, Calendar.getInstance().get(Calendar.YEAR));
                return cal.getTime();
            } else {
                // 包含年份
                return DATE_FORMAT_WITH_YEAR.parse(dateStr);
            }
        } catch (ParseException e) {
            logger.warn("Failed to parse date: {}", dateStr);
            return new Date();
        }
    }
    
    /**
     * 获取根目录列表
     * @param session SSH会话
     * @return 根目录列表
     * @throws Exception 操作异常
     */
    public List<String> getRootDirectories(Session session) throws Exception {
        List<String> roots = new ArrayList<>();
        
        // 添加常见的根目录
        roots.add("/");
        
        // 尝试获取用户主目录
        try {
            String homeDir = connectionManager.executeCommand(session, "echo $HOME").trim();
            if (!homeDir.isEmpty() && !"/".equals(homeDir)) {
                roots.add(homeDir);
            }
        } catch (Exception e) {
            logger.warn("Failed to get home directory", e);
        }
        
        return roots;
    }
    
    /**
     * 创建目录
     * @param session SSH会话
     * @param path 目录路径
     * @throws Exception 操作异常
     */
    public void createDirectory(Session session, String path) throws Exception {
        logger.info("Creating directory: {}", path);
        String command = "mkdir -p " + escapePath(path);
        connectionManager.executeCommand(session, command);
    }
    
    /**
     * 删除文件或目录
     * @param session SSH会话
     * @param path 文件路径
     * @param isDirectory 是否为目录
     * @throws Exception 操作异常
     */
    public void delete(Session session, String path, boolean isDirectory) throws Exception {
        logger.info("Deleting {}: {}", isDirectory ? "directory" : "file", path);
        String command = isDirectory ? "rm -rf " + escapePath(path) : "rm -f " + escapePath(path);
        connectionManager.executeCommand(session, command);
    }
    
    /**
     * 重命名文件或目录
     * @param session SSH会话
     * @param oldPath 原路径
     * @param newPath 新路径
     * @throws Exception 操作异常
     */
    public void rename(Session session, String oldPath, String newPath) throws Exception {
        logger.info("Renaming {} to {}", oldPath, newPath);
        String command = "mv " + escapePath(oldPath) + " " + escapePath(newPath);
        connectionManager.executeCommand(session, command);
    }
    
    /**
     * 上传文件
     * @param session SSH会话
     * @param localFile 本地文件
     * @param remotePath 远程路径
     * @param progressCallback 进度回调
     * @throws Exception 操作异常
     */
    public void uploadFile(Session session, File localFile, String remotePath, 
                          ProgressCallback progressCallback) throws Exception {
        logger.info("Uploading file {} to {}", localFile.getAbsolutePath(), remotePath);
        
        ChannelSftp sftpChannel = connectionManager.createSftpChannel(session);
        try {
            if (progressCallback != null) {
                sftpChannel.put(localFile.getAbsolutePath(), remotePath, new SftpProgressMonitor() {
                    private long totalBytes = localFile.length();
                    private long transferredBytes = 0;
                    
                    @Override
                    public void init(int op, String src, String dest, long max) {
                        progressCallback.onProgress(0, totalBytes);
                    }
                    
                    @Override
                    public boolean count(long count) {
                        transferredBytes += count;
                        progressCallback.onProgress(transferredBytes, totalBytes);
                        return true;
                    }
                    
                    @Override
                    public void end() {
                        progressCallback.onComplete();
                    }
                });
            } else {
                sftpChannel.put(localFile.getAbsolutePath(), remotePath);
            }
        } finally {
            connectionManager.disconnect(sftpChannel);
        }
    }
    
    /**
     * 下载文件
     * @param session SSH会话
     * @param remotePath 远程路径
     * @param localFile 本地文件
     * @param progressCallback 进度回调
     * @throws Exception 操作异常
     */
    public void downloadFile(Session session, String remotePath, File localFile, 
                           ProgressCallback progressCallback) throws Exception {
        logger.info("Downloading file {} to {}", remotePath, localFile.getAbsolutePath());
        
        ChannelSftp sftpChannel = connectionManager.createSftpChannel(session);
        try {
            if (progressCallback != null) {
                // 获取远程文件大小
                SftpATTRS attrs = sftpChannel.stat(remotePath);
                long fileSize = attrs.getSize();
                
                sftpChannel.get(remotePath, localFile.getAbsolutePath(), new SftpProgressMonitor() {
                    private long transferredBytes = 0;
                    
                    @Override
                    public void init(int op, String src, String dest, long max) {
                        progressCallback.onProgress(0, fileSize);
                    }
                    
                    @Override
                    public boolean count(long count) {
                        transferredBytes += count;
                        progressCallback.onProgress(transferredBytes, fileSize);
                        return true;
                    }
                    
                    @Override
                    public void end() {
                        progressCallback.onComplete();
                    }
                });
            } else {
                sftpChannel.get(remotePath, localFile.getAbsolutePath());
            }
        } finally {
            connectionManager.disconnect(sftpChannel);
        }
    }
    
    /**
     * 转义路径中的特殊字符
     * @param path 原路径
     * @return 转义后的路径
     */
    private String escapePath(String path) {
        return "\"" + path.replace("\"", "\\\"") + "\"";
    }
    
    /**
     * 进度回调接口
     */
    public interface ProgressCallback {
        void onProgress(long transferred, long total);
        void onComplete();
    }
}
