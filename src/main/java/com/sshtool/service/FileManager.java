package com.sshtool.service;

import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;
import com.jcraft.jsch.SftpATTRS;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.*;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.Vector;

/**
 * 文件管理器类，负责处理文件的上传、下载、浏览等操作
 */
public class FileManager {
    private static final Logger logger = LogManager.getLogger(FileManager.class);
    private ConnectionManager connectionManager;
    
    /**
     * 构造函数，初始化文件管理器
     */
    public FileManager() {
        this.connectionManager = new ConnectionManager();
    }
    
    /**
     * 获取根目录列表
     * @param session SSH会话对象
     * @return 根目录列表
     * @throws JSchException SFTP操作可能发生的异常
     */
    public List<String> getRootDirectories(Session session) throws JSchException {
        List<String> directories = new ArrayList<>();
        ChannelSftp channel = null;
        
        try {
            // 打开SFTP通道
            channel = connectionManager.openSftpChannel(session);
            channel.connect();
            
            // 列出根目录下的内容
            Vector<ChannelSftp.LsEntry> entries = channel.ls("/");
            for (ChannelSftp.LsEntry entry : entries) {
                // 排除当前目录(.)和父目录(..)
                if (!entry.getFilename().equals(".") && !entry.getFilename().equals("..")) {
                    SftpATTRS attrs = entry.getAttrs();
                    if (attrs.isDir()) {
                        directories.add(entry.getFilename());
                    }
                }
            }
            
        } catch (Exception e) {
            logger.error("Error getting root directories: {}", e.getMessage());
            throw new JSchException("Failed to get root directories: " + e.getMessage());
        } finally {
            if (channel != null && channel.isConnected()) {
                channel.disconnect();
            }
        }
        
        return directories;
    }
    
    /**
     * 列出目录内容
     * @param session SSH会话对象
     * @param path 要列出的目录路径
     * @return 目录内容列表
     * @throws JSchException SFTP操作可能发生的异常
     */
    public List<String> listFiles(Session session, String path) throws JSchException {
        List<String> files = new ArrayList<>();
        ChannelSftp channel = null;
        
        try {
            // 打开SFTP通道
            channel = connectionManager.openSftpChannel(session);
            channel.connect();
            
            // 列出目录内容
            Vector<ChannelSftp.LsEntry> entries = channel.ls(path);
            for (ChannelSftp.LsEntry entry : entries) {
                // 排除当前目录(.)和父目录(..)
                if (!entry.getFilename().equals(".") && !entry.getFilename().equals("..")) {
                    files.add(entry.getFilename());
                }
            }
            
        } catch (Exception e) {
            logger.error("Error listing files in {}: {}", path, e.getMessage());
            throw new JSchException("Failed to list files: " + e.getMessage());
        } finally {
            if (channel != null && channel.isConnected()) {
                channel.disconnect();
            }
        }
        
        return files;
    }
    
    /**
     * 下载文件
     * @param session SSH会话对象
     * @param remoteFilePath 远程文件路径
     * @param localFilePath 本地文件路径
     * @throws Exception 下载过程中可能发生的异常
     */
    public void downloadFile(Session session, String remoteFilePath, String localFilePath) throws Exception {
        logger.info("Downloading file from {} to {}", remoteFilePath, localFilePath);
        
        ChannelSftp channel = null;
        
        try {
            // 打开SFTP通道
            channel = connectionManager.openSftpChannel(session);
            channel.connect();
            
            // 创建本地目录（如果不存在）
            File localFile = new File(localFilePath);
            File parentDir = localFile.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                parentDir.mkdirs();
            }
            
            // 下载文件
            channel.get(remoteFilePath, localFilePath);
            logger.info("File downloaded successfully");
            
        } catch (Exception e) {
            logger.error("Error downloading file: {}", e.getMessage());
            throw new Exception("Failed to download file: " + e.getMessage());
        } finally {
            if (channel != null && channel.isConnected()) {
                channel.disconnect();
            }
        }
    }
    
    /**
     * 上传文件
     * @param session SSH会话对象
     * @param localFilePath 本地文件路径
     * @param remoteFilePath 远程文件路径
     * @throws Exception 上传过程中可能发生的异常
     */
    public void uploadFile(Session session, String localFilePath, String remoteFilePath) throws Exception {
        logger.info("Uploading file from {} to {}", localFilePath, remoteFilePath);
        
        ChannelSftp channel = null;
        
        try {
            // 检查本地文件是否存在
            File localFile = new File(localFilePath);
            if (!localFile.exists() || !localFile.isFile()) {
                throw new FileNotFoundException("Local file not found: " + localFilePath);
            }
            
            // 打开SFTP通道
            channel = connectionManager.openSftpChannel(session);
            channel.connect();
            
            // 确保远程目录存在
            String remoteDir = new File(remoteFilePath).getParent();
            if (remoteDir != null) {
                createRemoteDirectory(channel, remoteDir);
            }
            
            // 上传文件
            channel.put(localFilePath, remoteFilePath);
            logger.info("File uploaded successfully");
            
        } catch (Exception e) {
            logger.error("Error uploading file: {}", e.getMessage());
            throw new Exception("Failed to upload file: " + e.getMessage());
        } finally {
            if (channel != null && channel.isConnected()) {
                channel.disconnect();
            }
        }
    }
    
    /**
     * 创建远程目录（支持递归创建多级目录）
     * @param channel SFTP通道
     * @param directory 要创建的目录路径
     * @throws Exception 创建目录过程中可能发生的异常
     */
    private void createRemoteDirectory(ChannelSftp channel, String directory) throws Exception {
        String[] dirs = directory.split("/");
        StringBuilder pathBuilder = new StringBuilder();
        
        for (String dir : dirs) {
            if (dir.isEmpty()) {
                // 处理绝对路径的第一个斜杠
                pathBuilder.append("/");
                continue;
            }
            
            pathBuilder.append(dir).append("/");
            String currentPath = pathBuilder.toString();
            
            try {
                // 尝试获取目录属性，如果不存在则会抛出异常
                channel.stat(currentPath);
            } catch (Exception e) {
                // 目录不存在，创建它
                try {
                    channel.mkdir(currentPath);
                } catch (Exception ex) {
                    throw new Exception("Failed to create directory: " + currentPath, ex);
                }
            }
        }
    }
    
    /**
     * 删除远程文件
     * @param session SSH会话对象
     * @param remoteFilePath 要删除的远程文件路径
     * @throws Exception 删除过程中可能发生的异常
     */
    public void deleteFile(Session session, String remoteFilePath) throws Exception {
        logger.info("Deleting remote file: {}", remoteFilePath);
        
        ChannelSftp channel = null;
        
        try {
            // 打开SFTP通道
            channel = connectionManager.openSftpChannel(session);
            channel.connect();
            
            // 删除文件
            channel.rm(remoteFilePath);
            logger.info("File deleted successfully");
            
        } catch (Exception e) {
            logger.error("Error deleting file: {}", e.getMessage());
            throw new Exception("Failed to delete file: " + e.getMessage());
        } finally {
            if (channel != null && channel.isConnected()) {
                channel.disconnect();
            }
        }
    }
    
    /**
     * 创建远程目录
     * @param session SSH会话对象
     * @param remoteDirPath 要创建的远程目录路径
     * @throws Exception 创建过程中可能发生的异常
     */
    public void createDirectory(Session session, String remoteDirPath) throws Exception {
        logger.info("Creating remote directory: {}", remoteDirPath);
        
        ChannelSftp channel = null;
        
        try {
            // 打开SFTP通道
            channel = connectionManager.openSftpChannel(session);
            channel.connect();
            
            // 创建目录
            createRemoteDirectory(channel, remoteDirPath);
            logger.info("Directory created successfully");
            
        } catch (Exception e) {
            logger.error("Error creating directory: {}", e.getMessage());
            throw new Exception("Failed to create directory: " + e.getMessage());
        } finally {
            if (channel != null && channel.isConnected()) {
                channel.disconnect();
            }
        }
    }
    
    /**
     * 获取文件属性
     * @param session SSH会话对象
     * @param remoteFilePath 远程文件路径
     * @return 文件属性对象
     * @throws Exception 获取属性过程中可能发生的异常
     */
    public SftpATTRS getFileAttributes(Session session, String remoteFilePath) throws Exception {
        ChannelSftp channel = null;
        
        try {
            // 打开SFTP通道
            channel = connectionManager.openSftpChannel(session);
            channel.connect();
            
            // 获取文件属性
            return channel.stat(remoteFilePath);
            
        } catch (Exception e) {
            logger.error("Error getting file attributes: {}", e.getMessage());
            throw new Exception("Failed to get file attributes: " + e.getMessage());
        } finally {
            if (channel != null && channel.isConnected()) {
                channel.disconnect();
            }
        }
    }
    
    /**
     * 重命名或移动文件
     * @param session SSH会话对象
     * @param oldFilePath 旧文件路径
     * @param newFilePath 新文件路径
     * @throws Exception 重命名过程中可能发生的异常
     */
    public void renameFile(Session session, String oldFilePath, String newFilePath) throws Exception {
        logger.info("Renaming file from {} to {}", oldFilePath, newFilePath);
        
        ChannelSftp channel = null;
        
        try {
            // 打开SFTP通道
            channel = connectionManager.openSftpChannel(session);
            channel.connect();
            
            // 重命名文件
            channel.rename(oldFilePath, newFilePath);
            logger.info("File renamed successfully");
            
        } catch (Exception e) {
            logger.error("Error renaming file: {}", e.getMessage());
            throw new Exception("Failed to rename file: " + e.getMessage());
        } finally {
            if (channel != null && channel.isConnected()) {
                channel.disconnect();
            }
        }
    }
}