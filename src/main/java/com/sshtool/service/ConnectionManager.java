package com.sshtool.service;

import com.jcraft.jsch.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.InputStream;
import java.io.OutputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * 连接管理器类，负责处理SSH连接的建立、维护和关闭
 */
public class ConnectionManager {
    private static final Logger logger = LogManager.getLogger(ConnectionManager.class);
    private JSch jsch;
    private Map<ChannelShell, OutputStream> shellOutputStreams = new HashMap<>();
    private Map<ChannelShell, InputStream> shellInputStreams = new HashMap<>();
    
    /**
     * 构造函数，初始化连接管理器
     */
    public ConnectionManager() {
        jsch = new JSch();
    }
    
    /**
     * 建立SSH连接
     * @param hostname 主机名或IP地址
     * @param port 端口号
     * @param username 用户名
     * @param password 密码
     * @param privateKeyPath 私钥文件路径
     * @param passphrase 私钥密码
     * @return 建立的会话对象
     * @throws JSchException 连接过程中可能发生的异常
     */
    public Session connect(String hostname, int port, String username, String password, String privateKeyPath, String passphrase) throws JSchException {
        logger.info("Attempting to connect to {}:{}", hostname, port);
        
        // 创建会话
        Session session = jsch.getSession(username, hostname, port);
        
        // 设置连接属性
        Properties config = new Properties();
        config.put("StrictHostKeyChecking", "no"); // 不验证主机密钥
        config.put("PreferredAuthentications", "publickey,password"); // 优先使用公钥认证
        config.put("Compression", "yes"); // 启用压缩
        config.put("CompressionLevel", "9"); // 设置压缩级别
        session.setConfig(config);
        
        // 设置超时时间（30秒）
        session.setTimeout(30000);
        
        // 设置认证信息
        if (privateKeyPath != null && !privateKeyPath.isEmpty()) {
            // 使用私钥认证
            if (passphrase != null && !passphrase.isEmpty()) {
                jsch.addIdentity(privateKeyPath, passphrase);
            } else {
                jsch.addIdentity(privateKeyPath);
            }
            logger.info("Using private key authentication: {}", privateKeyPath);
        } else if (password != null && !password.isEmpty()) {
            // 使用密码认证
            session.setPassword(password);
            logger.info("Using password authentication");
        }
        
        // 连接到服务器
        session.connect();
        logger.info("Successfully connected to {}:{}", hostname, port);
        
        return session;
    }
    
    /**
     * 打开Shell通道
     * @param session 会话对象
     * @return Shell通道对象
     * @throws JSchException 打开通道过程中可能发生的异常
     */
    public ChannelShell openShellChannel(Session session) throws JSchException {
        if (session == null || !session.isConnected()) {
            throw new JSchException("Session is not connected");
        }
        
        // 打开Shell通道
        ChannelShell channel = (ChannelShell) session.openChannel("shell");
        
        // 设置通道的PTY类型
        channel.setPtyType("xterm");
        
        // 设置环境变量
        channel.setEnv("TERM", "xterm");
        channel.setEnv("LANG", "en_US.UTF-8");
        
        return channel;
    }
    
    /**
     * 获取Shell通道的输出流（用于发送命令）
     * @return Shell输出流
     */
    public OutputStream getShellOutput() {
        // 这里我们假设只返回最新打开的Shell通道的输出流
        // 实际应用中可能需要管理多个通道
        if (shellOutputStreams.isEmpty()) {
            return null;
        }
        return shellOutputStreams.values().iterator().next();
    }
    
    /**
     * 获取Shell通道的输入流（用于读取输出）
     * @return Shell输入流
     */
    public InputStream getShellInput() {
        // 这里我们假设只返回最新打开的Shell通道的输入流
        // 实际应用中可能需要管理多个通道
        if (shellInputStreams.isEmpty()) {
            return null;
        }
        return shellInputStreams.values().iterator().next();
    }
    
    /**
     * 设置Shell通道的输入输出流
     * @param channel Shell通道
     * @throws Exception 设置过程中可能发生的异常
     */
    public void setupShellStreams(ChannelShell channel) throws Exception {
        if (channel == null) {
            throw new IllegalArgumentException("Channel cannot be null");
        }
        
        OutputStream os = channel.getOutputStream();
        InputStream is = channel.getInputStream();
        
        shellOutputStreams.put(channel, os);
        shellInputStreams.put(channel, is);
        
        // 创建一个线程来监听通道状态，清理资源
        new Thread(() -> {
            while (true) {
                try {
                    Thread.sleep(1000);
                    if (!channel.isConnected()) {
                        shellOutputStreams.remove(channel);
                        shellInputStreams.remove(channel);
                        break;
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }).start();
    }
    
    /**
     * 判断连接管理器是否已连接
     * @return 是否已连接
     */
    public boolean isConnected() {
        // 如果有活动的Shell通道，则认为已连接
        return !shellOutputStreams.isEmpty() && !shellInputStreams.isEmpty();
    }
    
    /**
     * 打开SFTP通道
     * @param session 会话对象
     * @return SFTP通道对象
     * @throws JSchException 打开通道过程中可能发生的异常
     */
    public ChannelSftp openSftpChannel(Session session) throws JSchException {
        if (session == null || !session.isConnected()) {
            throw new JSchException("Session is not connected");
        }
        
        // 打开SFTP通道
        ChannelSftp channel = (ChannelSftp) session.openChannel("sftp");
        
        return channel;
    }
    
    /**
     * 执行单个命令
     * @param session 会话对象
     * @param command 要执行的命令
     * @return 命令执行结果
     * @throws JSchException 执行命令过程中可能发生的异常
     * @throws java.io.IOException 读取结果过程中可能发生的异常
     */
    public String executeCommand(Session session, String command) throws JSchException, java.io.IOException {
        if (session == null || !session.isConnected()) {
            throw new JSchException("Session is not connected");
        }
        
        // 打开执行命令的通道
        ChannelExec channel = (ChannelExec) session.openChannel("exec");
        channel.setCommand(command);
        
        // 获取输入流，读取命令执行结果
        java.io.InputStream in = channel.getInputStream();
        java.io.InputStream err = channel.getErrStream();
        
        // 连接通道
        channel.connect();
        
        // 读取标准输出
        StringBuilder output = new StringBuilder();
        byte[] buffer = new byte[1024];
        int bytesRead;
        while ((bytesRead = in.read(buffer)) != -1) {
            output.append(new String(buffer, 0, bytesRead));
        }
        
        // 读取错误输出
        StringBuilder errorOutput = new StringBuilder();
        while ((bytesRead = err.read(buffer)) != -1) {
            errorOutput.append(new String(buffer, 0, bytesRead));
        }
        
        // 如果有错误输出，记录日志
        if (errorOutput.length() > 0) {
            logger.warn("Command execution error: {}", errorOutput.toString());
        }
        
        // 断开通道连接
        channel.disconnect();
        
        return output.toString();
    }
    
    /**
     * 断开会话连接
     * @param session 要断开的会话对象
     */
    public void disconnect(Session session) {
        if (session != null && session.isConnected()) {
            session.disconnect();
            logger.info("Session disconnected");
        }
    }
    
    /**
     * 检查会话是否连接
     * @param session 会话对象
     * @return 是否连接
     */
    public boolean isConnected(Session session) {
        return session != null && session.isConnected();
    }
    
    /**
     * 获取会话的连接信息
     * @param session 会话对象
     * @return 连接信息字符串
     */
    public String getConnectionInfo(Session session) {
        if (session == null || !session.isConnected()) {
            return "Not connected";
        }
        
        return session.getUserName() + "@" + session.getHost() + ":" + session.getPort();
    }
}