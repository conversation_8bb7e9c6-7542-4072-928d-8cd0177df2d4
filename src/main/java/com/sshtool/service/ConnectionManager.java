package com.sshtool.service;

import com.jcraft.jsch.*;
import com.sshtool.model.ConnectionInfo;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.*;
import java.util.Properties;

/**
 * 连接管理器，负责SSH连接的建立、维护和断开
 */
public class ConnectionManager {
    private static final Logger logger = LogManager.getLogger(ConnectionManager.class);
    private JSch jsch;
    
    /**
     * 构造函数，初始化JSch
     */
    public ConnectionManager() {
        this.jsch = new JSch();
        // 设置已知主机检查策略
        JSch.setConfig("StrictHostKeyChecking", "no");
    }
    
    /**
     * 建立SSH连接
     * @param connectionInfo 连接信息
     * @return SSH会话对象
     * @throws JSchException 连接异常
     */
    public Session createSession(ConnectionInfo connectionInfo) throws JSchException {
        if (!connectionInfo.isValid()) {
            throw new IllegalArgumentException("Invalid connection information");
        }
        
        logger.info("Creating SSH session to {}@{}:{}", 
            connectionInfo.getUsername(), connectionInfo.getHostname(), connectionInfo.getPort());
        
        Session session = jsch.getSession(
            connectionInfo.getUsername(), 
            connectionInfo.getHostname(), 
            connectionInfo.getPort()
        );
        
        // 设置连接属性
        Properties config = new Properties();
        config.put("StrictHostKeyChecking", "no");
        config.put("PreferredAuthentications", "publickey,keyboard-interactive,password");
        config.put("ConnectTimeout", "30000"); // 30秒超时
        config.put("ServerAliveInterval", "60"); // 60秒心跳
        config.put("ServerAliveCountMax", "3");
        session.setConfig(config);
        
        // 设置认证方式
        if (connectionInfo.isUsePrivateKey()) {
            // 使用私钥认证
            try {
                if (connectionInfo.getPassphrase() != null && !connectionInfo.getPassphrase().isEmpty()) {
                    jsch.addIdentity(connectionInfo.getPrivateKeyPath(), connectionInfo.getPassphrase());
                } else {
                    jsch.addIdentity(connectionInfo.getPrivateKeyPath());
                }
                logger.info("Using private key authentication: {}", connectionInfo.getPrivateKeyPath());
            } catch (JSchException e) {
                logger.error("Failed to load private key: {}", e.getMessage());
                throw e;
            }
        } else {
            // 使用密码认证
            session.setPassword(connectionInfo.getPassword());
            logger.info("Using password authentication");
        }
        
        return session;
    }
    
    /**
     * 连接到SSH服务器
     * @param session SSH会话对象
     * @throws JSchException 连接异常
     */
    public void connect(Session session) throws JSchException {
        logger.info("Connecting to SSH server...");
        session.connect();
        logger.info("SSH connection established successfully");
    }
    
    /**
     * 创建Shell通道
     * @param session SSH会话对象
     * @return Shell通道
     * @throws JSchException 通道创建异常
     */
    public ChannelShell createShellChannel(Session session) throws JSchException {
        logger.info("Creating shell channel");
        ChannelShell channel = (ChannelShell) session.openChannel("shell");
        
        // 设置终端类型和大小
        channel.setPtyType("xterm");
        channel.setPtySize(120, 40, 0, 0);
        
        return channel;
    }
    
    /**
     * 创建SFTP通道
     * @param session SSH会话对象
     * @return SFTP通道
     * @throws JSchException 通道创建异常
     */
    public ChannelSftp createSftpChannel(Session session) throws JSchException {
        logger.info("Creating SFTP channel");
        ChannelSftp channel = (ChannelSftp) session.openChannel("sftp");
        channel.connect();
        return channel;
    }
    
    /**
     * 执行单个命令
     * @param session SSH会话对象
     * @param command 要执行的命令
     * @return 命令输出结果
     * @throws JSchException 执行异常
     * @throws IOException IO异常
     */
    public String executeCommand(Session session, String command) throws JSchException, IOException {
        logger.debug("Executing command: {}", command);
        
        ChannelExec channel = (ChannelExec) session.openChannel("exec");
        channel.setCommand(command);
        
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ByteArrayOutputStream errorStream = new ByteArrayOutputStream();
        
        channel.setOutputStream(outputStream);
        channel.setErrStream(errorStream);
        
        channel.connect();
        
        // 等待命令执行完成
        while (!channel.isClosed()) {
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        
        int exitCode = channel.getExitStatus();
        channel.disconnect();
        
        String output = outputStream.toString("UTF-8");
        String error = errorStream.toString("UTF-8");
        
        if (exitCode != 0 && !error.isEmpty()) {
            logger.warn("Command execution failed with exit code {}: {}", exitCode, error);
        }
        
        logger.debug("Command output: {}", output);
        return output;
    }
    
    /**
     * 测试连接
     * @param connectionInfo 连接信息
     * @return 连接是否成功
     */
    public boolean testConnection(ConnectionInfo connectionInfo) {
        Session session = null;
        try {
            session = createSession(connectionInfo);
            connect(session);
            
            // 执行简单命令测试
            String result = executeCommand(session, "echo 'Connection test successful'");
            return result.contains("Connection test successful");
            
        } catch (Exception e) {
            logger.error("Connection test failed: {}", e.getMessage());
            return false;
        } finally {
            if (session != null && session.isConnected()) {
                session.disconnect();
            }
        }
    }
    
    /**
     * 断开连接
     * @param session SSH会话对象
     */
    public void disconnect(Session session) {
        if (session != null && session.isConnected()) {
            logger.info("Disconnecting SSH session");
            session.disconnect();
        }
    }
    
    /**
     * 断开通道连接
     * @param channel 通道对象
     */
    public void disconnect(Channel channel) {
        if (channel != null && channel.isConnected()) {
            logger.info("Disconnecting channel: {}", channel.getClass().getSimpleName());
            channel.disconnect();
        }
    }
    
    /**
     * 获取连接状态信息
     * @param session SSH会话对象
     * @return 状态信息字符串
     */
    public String getConnectionStatus(Session session) {
        if (session == null) {
            return "No session";
        }
        
        if (session.isConnected()) {
            return String.format("Connected to %s@%s:%d", 
                session.getUserName(), session.getHost(), session.getPort());
        } else {
            return "Disconnected";
        }
    }
    
    /**
     * 设置连接超时时间
     * @param session SSH会话对象
     * @param timeout 超时时间（毫秒）
     */
    public void setConnectionTimeout(Session session, int timeout) {
        if (session != null) {
            try {
                session.setTimeout(timeout);
            } catch (JSchException e) {
                logger.warn("Failed to set connection timeout: {}", e.getMessage());
            }
        }
    }
}
