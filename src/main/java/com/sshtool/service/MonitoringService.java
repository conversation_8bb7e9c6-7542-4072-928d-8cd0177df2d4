package com.sshtool.service;

import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;
import com.sshtool.model.ServerStats;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.IOException;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 监控服务类，负责实时监控服务器的CPU、内存、磁盘和网络流量等状态
 */
public class MonitoringService {
    private static final Logger logger = LogManager.getLogger(MonitoringService.class);
    private ConnectionManager connectionManager;
    private ScheduledExecutorService scheduler;
    private boolean isMonitoring;
    private long lastNetworkRxBytes;
    private long lastNetworkTxBytes;
    private long lastTimestamp;
    
    /**
     * 构造函数，初始化监控服务
     */
    public MonitoringService() {
        this.connectionManager = new ConnectionManager();
        this.scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread thread = Executors.defaultThreadFactory().newThread(r);
            thread.setDaemon(true); // 设置为守护线程，主线程结束时自动终止
            thread.setName("monitoring-thread");
            return thread;
        });
        this.isMonitoring = false;
    }
    
    /**
     * 开始监控服务器状态
     * @param session SSH会话对象
     * @param callback 回调函数，用于处理监控数据
     */
    public void startMonitoring(Session session, MonitoringCallback callback) {
        if (isMonitoring) {
            logger.warn("Monitoring already started");
            return;
        }
        
        if (session == null || !session.isConnected()) {
            logger.error("Cannot start monitoring: session is not connected");
            return;
        }
        
        logger.info("Starting server monitoring");
        isMonitoring = true;
        
        // 初始化网络流量计数器
        try {
            String netstatOutput = connectionManager.executeCommand(session, "cat /proc/net/dev");
            parseNetworkStats(netstatOutput);
            lastTimestamp = System.currentTimeMillis();
        } catch (Exception e) {
            logger.error("Failed to initialize network monitoring: {}", e.getMessage());
        }
        
        // 定期执行监控任务，每2秒一次
        scheduler.scheduleAtFixedRate(() -> {
            if (!session.isConnected()) {
                logger.warn("Session disconnected, stopping monitoring");
                stopMonitoring();
                return;
            }
            
            try {
                // 获取服务器状态
                ServerStats stats = getServerStats(session);
                
                // 调用回调函数处理数据
                if (callback != null) {
                    callback.onStatsUpdated(stats);
                }
            } catch (Exception e) {
                logger.error("Error during monitoring: {}", e.getMessage());
            }
        }, 0, 2, TimeUnit.SECONDS);
    }
    
    /**
     * 停止监控服务
     */
    public void stopMonitoring() {
        if (!isMonitoring) {
            return;
        }
        
        logger.info("Stopping server monitoring");
        isMonitoring = false;
        scheduler.shutdown();
        try {
            // 等待监控线程终止，最多等待1秒
            if (!scheduler.awaitTermination(1, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
        
        // 重新创建调度器，以便下次监控使用
        scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread thread = Executors.defaultThreadFactory().newThread(r);
            thread.setDaemon(true);
            thread.setName("monitoring-thread");
            return thread;
        });
    }
    
    /**
     * 获取服务器状态统计信息
     * @param session SSH会话对象
     * @return 服务器状态统计信息
     * @throws JSchException SSH操作可能发生的异常
     * @throws IOException IO操作可能发生的异常
     */
    private ServerStats getServerStats(Session session) throws JSchException, IOException {
        ServerStats stats = new ServerStats();
        
        // 获取CPU使用率
        stats.setCpuUsage(getCpuUsage(session));
        
        // 获取内存使用率
        stats.setMemoryUsage(getMemoryUsage(session));
        
        // 获取磁盘使用率
        stats.setDiskUsage(getDiskUsage(session));
        
        // 获取网络流量
        long currentTimestamp = System.currentTimeMillis();
        double[] networkRates = getNetworkRates(session, currentTimestamp);
        stats.setNetworkRx(networkRates[0]); // 接收速率
        stats.setNetworkTx(networkRates[1]); // 发送速率
        
        // 获取系统运行时间和负载
        String uptimeOutput = connectionManager.executeCommand(session, "uptime");
        parseUptime(uptimeOutput, stats);
        
        return stats;
    }
    
    /**
     * 获取CPU使用率
     * @param session SSH会话对象
     * @return CPU使用率 (%)
     * @throws JSchException SSH操作可能发生的异常
     * @throws IOException IO操作可能发生的异常
     */
    private double getCpuUsage(Session session) throws JSchException, IOException {
        String cpuInfo = connectionManager.executeCommand(session, "cat /proc/stat | grep cpu");
        String[] lines = cpuInfo.split("\\n");
        
        // 处理第一行，获取总的CPU使用率
        String[] parts = lines[0].split("\\s+");
        long user = Long.parseLong(parts[1]);
        long nice = Long.parseLong(parts[2]);
        long system = Long.parseLong(parts[3]);
        long idle = Long.parseLong(parts[4]);
        long iowait = Long.parseLong(parts[5]);
        long irq = Long.parseLong(parts[6]);
        long softirq = Long.parseLong(parts[7]);
        
        long totalCpuTime = user + nice + system + idle + iowait + irq + softirq;
        long usedCpuTime = totalCpuTime - idle;
        
        return (double) usedCpuTime / totalCpuTime * 100.0;
    }
    
    /**
     * 获取内存使用率
     * @param session SSH会话对象
     * @return 内存使用率 (%)
     * @throws JSchException SSH操作可能发生的异常
     * @throws IOException IO操作可能发生的异常
     */
    private double getMemoryUsage(Session session) throws JSchException, IOException {
        String memInfo = connectionManager.executeCommand(session, "cat /proc/meminfo");
        
        long totalMem = 0;
        long freeMem = 0;
        long buffers = 0;
        long cached = 0;
        
        String[] lines = memInfo.split("\\n");
        for (String line : lines) {
            if (line.startsWith("MemTotal:")) {
                totalMem = Long.parseLong(line.split("\\s+")[1]);
            } else if (line.startsWith("MemFree:")) {
                freeMem = Long.parseLong(line.split("\\s+")[1]);
            } else if (line.startsWith("Buffers:")) {
                buffers = Long.parseLong(line.split("\\s+")[1]);
            } else if (line.startsWith("Cached:")) {
                cached = Long.parseLong(line.split("\\s+")[1]);
            }
        }
        
        // 计算实际使用的内存
        long usedMem = totalMem - freeMem - buffers - cached;
        
        return (double) usedMem / totalMem * 100.0;
    }
    
    /**
     * 获取磁盘使用率
     * @param session SSH会话对象
     * @return 磁盘使用率 (%)
     * @throws JSchException SSH操作可能发生的异常
     * @throws IOException IO操作可能发生的异常
     */
    private double getDiskUsage(Session session) throws JSchException, IOException {
        String dfOutput = connectionManager.executeCommand(session, "df -h | grep '^/dev/'");
        String[] lines = dfOutput.split("\\n");
        
        // 取第一行作为主要磁盘
        if (lines.length > 0) {
            String[] parts = lines[0].split("\\s+");
            // 最后一列是使用率，格式如 "30%"
            String usageStr = parts[parts.length - 1];
            return Double.parseDouble(usageStr.replace("%", ""));
        }
        
        return 0.0;
    }
    
    /**
     * 获取网络传输速率
     * @param session SSH会话对象
     * @param currentTimestamp 当前时间戳
     * @return 网络传输速率数组 [接收速率(MB/s), 发送速率(MB/s)]
     * @throws JSchException SSH操作可能发生的异常
     * @throws IOException IO操作可能发生的异常
     */
    private double[] getNetworkRates(Session session, long currentTimestamp) throws JSchException, IOException {
        double rxRate = 0.0;
        double txRate = 0.0;
        
        // 检查是否是第一次获取网络流量
        if (lastTimestamp > 0) {
            String netstatOutput = connectionManager.executeCommand(session, "cat /proc/net/dev");
            long[] currentStats = parseNetworkStats(netstatOutput);
            long currentRxBytes = currentStats[0];
            long currentTxBytes = currentStats[1];
            
            // 计算时间差（秒）
            double timeDiff = (currentTimestamp - lastTimestamp) / 1000.0;
            
            // 计算速率（MB/s）
            rxRate = (currentRxBytes - lastNetworkRxBytes) / timeDiff / (1024 * 1024);
            txRate = (currentTxBytes - lastNetworkTxBytes) / timeDiff / (1024 * 1024);
            
            // 更新计数器
            lastNetworkRxBytes = currentRxBytes;
            lastNetworkTxBytes = currentTxBytes;
            lastTimestamp = currentTimestamp;
        }
        
        return new double[]{rxRate, txRate};
    }
    
    /**
     * 解析网络统计信息
     * @param output /proc/net/dev 文件的内容
     * @return 网络统计数组 [接收字节数, 发送字节数]
     */
    private long[] parseNetworkStats(String output) {
        long totalRxBytes = 0;
        long totalTxBytes = 0;
        
        String[] lines = output.split("\\n");
        for (String line : lines) {
            line = line.trim();
            if (line.contains(":")) {
                String[] parts = line.split(":").length > 1 ? line.split(":")[1].trim().split("\\s+") : new String[0];
                if (parts.length >= 10) {
                    try {
                        // 第一个数字是接收字节数
                        totalRxBytes += Long.parseLong(parts[0]);
                        // 第十个数字是发送字节数
                        totalTxBytes += Long.parseLong(parts[8]);
                    } catch (NumberFormatException e) {
                        logger.warn("Failed to parse network stats: {}", e.getMessage());
                    }
                }
            }
        }
        
        // 如果是第一次解析，初始化计数器
        if (lastTimestamp == 0) {
            lastNetworkRxBytes = totalRxBytes;
            lastNetworkTxBytes = totalTxBytes;
        }
        
        return new long[]{totalRxBytes, totalTxBytes};
    }
    
    /**
     * 解析系统运行时间信息
     * @param uptimeOutput uptime命令的输出
     * @param stats 服务器统计信息对象
     */
    private void parseUptime(String uptimeOutput, ServerStats stats) {
        String[] parts = uptimeOutput.split(",");
        if (parts.length > 2) {
            try {
                // 提取系统运行时间
                String uptimePart = parts[0].trim();
                String[] uptimeParts = uptimePart.split("up")[1].trim().split(" ");
                if (uptimeParts.length >= 2) {
                    if (uptimeParts[1].contains("min")) {
                        long minutes = Long.parseLong(uptimeParts[0]);
                        stats.setUptime(minutes * 60);
                    } else if (uptimeParts[1].contains("day")) {
                        long days = Long.parseLong(uptimeParts[0]);
                        if (uptimeParts.length >= 3) {
                            String timePart = uptimeParts[2];
                            if (timePart.contains(":")) {
                                String[] timeParts = timePart.split(":");
                                long hours = Long.parseLong(timeParts[0]);
                                long minutes = Long.parseLong(timeParts[1]);
                                stats.setUptime((days * 24 + hours) * 3600 + minutes * 60);
                            }
                        }
                    }
                }
                
                // 提取负载平均值（1分钟、5分钟、15分钟）
                if (parts.length >= 4) {
                    String loadPart = parts[3].trim();
                    if (loadPart.startsWith("load average:")) {
                        String[] loadParts = loadPart.split(":")[1].trim().split(",");
                        if (loadParts.length >= 1) {
                            double loadAvg = Double.parseDouble(loadParts[0].trim());
                            stats.setLoadAverage((int) Math.round(loadAvg));
                        }
                    }
                }
            } catch (Exception e) {
                logger.warn("Failed to parse uptime: {}", e.getMessage());
            }
        }
    }
    
    /**
     * 检查是否正在监控
     * @return 是否正在监控
     */
    public boolean isMonitoring() {
        return isMonitoring;
    }
    
    /**
     * 监控回调接口
     */
    public interface MonitoringCallback {
        /**
         * 当服务器状态更新时调用
         * @param stats 服务器状态统计信息
         */
        void onStatsUpdated(ServerStats stats);
    }
}