package com.sshtool.service;

import com.jcraft.jsch.Session;
import com.sshtool.model.ServerStats;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 监控服务类，负责实时监控服务器的CPU、内存、磁盘和网络流量等状态
 */
public class MonitoringService {
    private static final Logger logger = LogManager.getLogger(MonitoringService.class);
    private ConnectionManager connectionManager;
    private ScheduledExecutorService scheduler;
    private boolean isMonitoring;
    private long lastNetworkRxBytes;
    private long lastNetworkTxBytes;
    private long lastTimestamp;
    
    /**
     * 构造函数，初始化监控服务
     */
    public MonitoringService() {
        this.connectionManager = new ConnectionManager();
        this.scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread thread = Executors.defaultThreadFactory().newThread(r);
            thread.setDaemon(true); // 设置为守护线程，主线程结束时自动终止
            thread.setName("monitoring-thread");
            return thread;
        });
        this.isMonitoring = false;
    }
    
    /**
     * 开始监控服务器状态
     * @param session SSH会话对象
     * @param callback 回调函数，用于处理监控数据
     */
    public void startMonitoring(Session session, MonitoringCallback callback) {
        if (isMonitoring) {
            logger.warn("Monitoring already started");
            return;
        }
        
        if (session == null || !session.isConnected()) {
            logger.error("Cannot start monitoring: session is not connected");
            return;
        }
        
        logger.info("Starting server monitoring");
        isMonitoring = true;
        
        // 初始化网络流量计数器
        try {
            String netstatOutput = connectionManager.executeCommand(session, "cat /proc/net/dev");
            parseNetworkStats(netstatOutput);
            lastTimestamp = System.currentTimeMillis();
        } catch (Exception e) {
            logger.error("Failed to initialize network monitoring: {}", e.getMessage());
        }
        
        // 定期执行监控任务，每2秒一次
        scheduler.scheduleAtFixedRate(() -> {
            if (!session.isConnected()) {
                logger.warn("Session disconnected, stopping monitoring");
                stopMonitoring();
                return;
            }
            
            try {
                // 获取服务器状态
                ServerStats stats = getServerStats(session);
                
                // 调用回调函数处理数据
                if (callback != null) {
                    callback.onStatsUpdated(stats);
                }
            } catch (Exception e) {
                logger.error("Error during monitoring: {}", e.getMessage());
            }
        }, 0, 2, TimeUnit.SECONDS);
    }
    
    /**
     * 停止监控
     */
    public void stopMonitoring() {
        if (isMonitoring) {
            logger.info("Stopping server monitoring");
            isMonitoring = false;
            scheduler.shutdownNow();
            
            // 重新创建调度器以备下次使用
            scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
                Thread thread = Executors.defaultThreadFactory().newThread(r);
                thread.setDaemon(true);
                thread.setName("monitoring-thread");
                return thread;
            });
        }
    }
    
    /**
     * 获取服务器统计信息
     * @param session SSH会话对象
     * @return 服务器统计信息
     * @throws Exception 获取异常
     */
    public ServerStats getServerStats(Session session) throws Exception {
        ServerStats stats = new ServerStats();
        
        // 获取CPU使用率
        double cpuUsage = getCpuUsage(session);
        stats.setCpuUsage(cpuUsage);
        
        // 获取内存使用情况
        double[] memoryStats = getMemoryUsage(session);
        stats.setMemoryUsage(memoryStats[0]);
        stats.setTotalMemory((long) memoryStats[1]);
        stats.setUsedMemory((long) memoryStats[2]);
        
        // 获取磁盘使用情况
        double[] diskStats = getDiskUsage(session);
        stats.setDiskUsage(diskStats[0]);
        stats.setTotalDisk((long) diskStats[1]);
        stats.setUsedDisk((long) diskStats[2]);
        
        // 获取网络流量
        double[] networkStats = getNetworkTraffic(session);
        stats.setNetworkRx(networkStats[0]);
        stats.setNetworkTx(networkStats[1]);
        
        // 获取系统信息
        long uptime = getUptime(session);
        stats.setUptime(uptime);
        
        int processCount = getProcessCount(session);
        stats.setProcessCount(processCount);
        
        double loadAverage = getLoadAverage(session);
        stats.setLoadAverage(loadAverage);
        
        stats.setTimestamp(System.currentTimeMillis());
        
        return stats;
    }
    
    /**
     * 获取CPU使用率
     * @param session SSH会话对象
     * @return CPU使用率百分比
     * @throws Exception 获取异常
     */
    private double getCpuUsage(Session session) throws Exception {
        // 使用top命令获取CPU使用率
        String command = "top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | sed 's/%us,//'";
        String output = connectionManager.executeCommand(session, command).trim();
        
        try {
            return Double.parseDouble(output);
        } catch (NumberFormatException e) {
            // 备用方法：使用vmstat
            command = "vmstat 1 2 | tail -1 | awk '{print 100-$15}'";
            output = connectionManager.executeCommand(session, command).trim();
            return Double.parseDouble(output);
        }
    }
    
    /**
     * 获取内存使用情况
     * @param session SSH会话对象
     * @return [使用率%, 总内存MB, 已用内存MB]
     * @throws Exception 获取异常
     */
    private double[] getMemoryUsage(Session session) throws Exception {
        String command = "free -m | grep '^Mem:'";
        String output = connectionManager.executeCommand(session, command).trim();
        
        String[] parts = output.split("\\s+");
        if (parts.length >= 3) {
            long total = Long.parseLong(parts[1]);
            long used = Long.parseLong(parts[2]);
            double usage = (double) used / total * 100;
            
            return new double[]{usage, total, used};
        }
        
        return new double[]{0, 0, 0};
    }
    
    /**
     * 获取磁盘使用情况
     * @param session SSH会话对象
     * @return [使用率%, 总空间GB, 已用空间GB]
     * @throws Exception 获取异常
     */
    private double[] getDiskUsage(Session session) throws Exception {
        String command = "df -h / | tail -1 | awk '{print $5 \" \" $2 \" \" $3}'";
        String output = connectionManager.executeCommand(session, command).trim();
        
        String[] parts = output.split("\\s+");
        if (parts.length >= 3) {
            String usageStr = parts[0].replace("%", "");
            double usage = Double.parseDouble(usageStr);
            
            // 解析大小（可能包含G、M等单位）
            double total = parseSize(parts[1]);
            double used = parseSize(parts[2]);
            
            return new double[]{usage, total, used};
        }
        
        return new double[]{0, 0, 0};
    }
    
    /**
     * 解析大小字符串（如1.5G, 512M）
     * @param sizeStr 大小字符串
     * @return 大小（GB）
     */
    private double parseSize(String sizeStr) {
        if (sizeStr.endsWith("G")) {
            return Double.parseDouble(sizeStr.substring(0, sizeStr.length() - 1));
        } else if (sizeStr.endsWith("M")) {
            return Double.parseDouble(sizeStr.substring(0, sizeStr.length() - 1)) / 1024;
        } else if (sizeStr.endsWith("K")) {
            return Double.parseDouble(sizeStr.substring(0, sizeStr.length() - 1)) / (1024 * 1024);
        } else {
            return Double.parseDouble(sizeStr) / (1024 * 1024 * 1024);
        }
    }
    
    /**
     * 获取网络流量
     * @param session SSH会话对象
     * @return [接收速率MB/s, 发送速率MB/s]
     * @throws Exception 获取异常
     */
    private double[] getNetworkTraffic(Session session) throws Exception {
        long currentTimestamp = System.currentTimeMillis();
        double rxRate = 0.0;
        double txRate = 0.0;
        
        // 检查是否是第一次获取网络流量
        if (lastTimestamp > 0) {
            String netstatOutput = connectionManager.executeCommand(session, "cat /proc/net/dev");
            long[] currentStats = parseNetworkStats(netstatOutput);
            long currentRxBytes = currentStats[0];
            long currentTxBytes = currentStats[1];
            
            // 计算时间差（秒）
            double timeDiff = (currentTimestamp - lastTimestamp) / 1000.0;
            
            // 计算速率（MB/s）
            rxRate = (currentRxBytes - lastNetworkRxBytes) / timeDiff / (1024 * 1024);
            txRate = (currentTxBytes - lastNetworkTxBytes) / timeDiff / (1024 * 1024);
            
            // 更新计数器
            lastNetworkRxBytes = currentRxBytes;
            lastNetworkTxBytes = currentTxBytes;
            lastTimestamp = currentTimestamp;
        }
        
        return new double[]{rxRate, txRate};
    }
    
    /**
     * 解析网络统计信息
     * @param netstatOutput /proc/net/dev的输出
     * @return [总接收字节数, 总发送字节数]
     */
    private long[] parseNetworkStats(String netstatOutput) {
        long totalRxBytes = 0;
        long totalTxBytes = 0;
        
        String[] lines = netstatOutput.split("\n");
        Pattern pattern = Pattern.compile("^\\s*(\\w+):\\s*(\\d+)\\s+\\d+\\s+\\d+\\s+\\d+\\s+\\d+\\s+\\d+\\s+\\d+\\s+\\d+\\s+(\\d+)");
        
        for (String line : lines) {
            Matcher matcher = pattern.matcher(line);
            if (matcher.matches()) {
                String interfaceName = matcher.group(1);
                // 跳过回环接口
                if (!"lo".equals(interfaceName)) {
                    totalRxBytes += Long.parseLong(matcher.group(2));
                    totalTxBytes += Long.parseLong(matcher.group(3));
                }
            }
        }
        
        return new long[]{totalRxBytes, totalTxBytes};
    }
    
    /**
     * 获取系统运行时间
     * @param session SSH会话对象
     * @return 运行时间（秒）
     * @throws Exception 获取异常
     */
    private long getUptime(Session session) throws Exception {
        String command = "cat /proc/uptime | awk '{print $1}'";
        String output = connectionManager.executeCommand(session, command).trim();
        return (long) Double.parseDouble(output);
    }
    
    /**
     * 获取进程数量
     * @param session SSH会话对象
     * @return 进程数量
     * @throws Exception 获取异常
     */
    private int getProcessCount(Session session) throws Exception {
        String command = "ps aux | wc -l";
        String output = connectionManager.executeCommand(session, command).trim();
        return Integer.parseInt(output) - 1; // 减去标题行
    }
    
    /**
     * 获取系统负载
     * @param session SSH会话对象
     * @return 1分钟平均负载
     * @throws Exception 获取异常
     */
    private double getLoadAverage(Session session) throws Exception {
        String command = "cat /proc/loadavg | awk '{print $1}'";
        String output = connectionManager.executeCommand(session, command).trim();
        return Double.parseDouble(output);
    }
    
    /**
     * 检查是否正在监控
     * @return 是否正在监控
     */
    public boolean isMonitoring() {
        return isMonitoring;
    }
    
    /**
     * 监控回调接口
     */
    public interface MonitoringCallback {
        void onStatsUpdated(ServerStats stats);
    }
}
