package com.sshtool.service;

import com.sshtool.model.ConnectionInfo;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 历史管理器类，负责持久化存储和管理用户的连接历史记录
 */
public class HistoryManager {
    private static final Logger logger = LogManager.getLogger(HistoryManager.class);
    private static final String CONFIG_DIR = System.getProperty("user.home") + File.separator + ".java-ssh-client";
    private static final String CONNECTIONS_FILE = CONFIG_DIR + File.separator + "connections.dat";
    private static final String COMMANDS_HISTORY_FILE = CONFIG_DIR + File.separator + "commands_history.dat";
    
    /**
     * 构造函数，初始化历史管理器
     */
    public HistoryManager() {
        // 确保配置目录存在
        try {
            Path configPath = Paths.get(CONFIG_DIR);
            if (!Files.exists(configPath)) {
                Files.createDirectories(configPath);
                logger.info("Created configuration directory: {}", CONFIG_DIR);
            }
        } catch (IOException e) {
            logger.error("Failed to create configuration directory: {}", e.getMessage());
        }
    }
    
    /**
     * 保存连接信息
     * @param connectionInfo 要保存的连接信息
     */
    public void saveConnection(ConnectionInfo connectionInfo) {
        if (connectionInfo == null) {
            logger.warn("ConnectionInfo is null, cannot save");
            return;
        }
        
        // 获取已保存的连接列表
        List<ConnectionInfo> connections = loadConnections();
        
        // 检查是否已存在相同的连接
        boolean updated = false;
        for (int i = 0; i < connections.size(); i++) {
            if (connections.get(i).equals(connectionInfo)) {
                // 更新已存在的连接
                connections.set(i, connectionInfo);
                updated = true;
                break;
            }
        }
        
        // 如果是新连接，添加到列表
        if (!updated) {
            connections.add(connectionInfo);
        }
        
        // 保存到文件
        saveConnectionsToFile(connections);
    }
    
    /**
     * 加载保存的连接信息列表
     * @return 连接信息列表
     */
    public List<ConnectionInfo> loadConnections() {
        File file = new File(CONNECTIONS_FILE);
        
        // 如果文件不存在，返回空列表
        if (!file.exists()) {
            logger.info("Connections file does not exist, returning empty list");
            return new ArrayList<>();
        }
        
        try (ObjectInputStream ois = new ObjectInputStream(new FileInputStream(file))) {
            @SuppressWarnings("unchecked")
            List<ConnectionInfo> connections = (List<ConnectionInfo>) ois.readObject();
            logger.info("Loaded {} connections from file", connections.size());
            return connections;
        } catch (IOException | ClassNotFoundException e) {
            logger.error("Failed to load connections: {}", e.getMessage());
            // 如果加载失败，返回空列表
            return new ArrayList<>();
        }
    }
    
    /**
     * 删除连接信息
     * @param connectionInfo 要删除的连接信息
     * @return 是否删除成功
     */
    public boolean deleteConnection(ConnectionInfo connectionInfo) {
        if (connectionInfo == null) {
            logger.warn("ConnectionInfo is null, cannot delete");
            return false;
        }
        
        // 获取已保存的连接列表
        List<ConnectionInfo> connections = loadConnections();
        
        // 移除匹配的连接
        boolean removed = connections.removeIf(conn -> conn.equals(connectionInfo));
        
        // 如果移除了连接，保存更新后的列表
        if (removed) {
            saveConnectionsToFile(connections);
            logger.info("Connection deleted successfully");
        } else {
            logger.info("Connection not found, nothing deleted");
        }
        
        return removed;
    }
    
    /**
     * 保存连接列表到文件
     * @param connections 要保存的连接列表
     */
    private void saveConnectionsToFile(List<ConnectionInfo> connections) {
        try (ObjectOutputStream oos = new ObjectOutputStream(new FileOutputStream(CONNECTIONS_FILE))) {
            oos.writeObject(connections);
            logger.info("Saved {} connections to file", connections.size());
        } catch (IOException e) {
            logger.error("Failed to save connections: {}", e.getMessage());
        }
    }
    
    /**
     * 保存命令历史
     * @param command 要保存的命令
     */
    public void saveCommandHistory(String command) {
        if (command == null || command.trim().isEmpty()) {
            return;
        }
        
        // 获取已保存的命令历史
        List<String> commands = loadCommandHistory();
        
        // 如果命令已存在，先移除（确保新命令在列表顶部）
        commands.remove(command);
        
        // 添加命令到列表顶部
        commands.add(0, command);
        
        // 限制历史记录大小为100条
        if (commands.size() > 100) {
            commands = commands.subList(0, 100);
        }
        
        // 保存到文件
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(COMMANDS_HISTORY_FILE))) {
            for (String cmd : commands) {
                writer.write(cmd);
                writer.newLine();
            }
        } catch (IOException e) {
            logger.error("Failed to save command history: {}", e.getMessage());
        }
    }
    
    /**
     * 加载命令历史
     * @return 命令历史列表
     */
    public List<String> loadCommandHistory() {
        File file = new File(COMMANDS_HISTORY_FILE);
        List<String> commands = new ArrayList<>();
        
        // 如果文件不存在，返回空列表
        if (!file.exists()) {
            return commands;
        }
        
        try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
            String line;
            while ((line = reader.readLine()) != null) {
                commands.add(line);
            }
        } catch (IOException e) {
            logger.error("Failed to load command history: {}", e.getMessage());
        }
        
        return commands;
    }
    
    /**
     * 清除所有连接历史
     * @return 是否清除成功
     */
    public boolean clearAllConnections() {
        File file = new File(CONNECTIONS_FILE);
        if (file.exists()) {
            boolean deleted = file.delete();
            if (deleted) {
                logger.info("All connections cleared");
            } else {
                logger.error("Failed to clear connections");
            }
            return deleted;
        }
        logger.info("No connections to clear");
        return true;
    }
    
    /**
     * 清除所有命令历史
     * @return 是否清除成功
     */
    public boolean clearAllCommandHistory() {
        File file = new File(COMMANDS_HISTORY_FILE);
        if (file.exists()) {
            boolean deleted = file.delete();
            if (deleted) {
                logger.info("All command history cleared");
            } else {
                logger.error("Failed to clear command history");
            }
            return deleted;
        }
        logger.info("No command history to clear");
        return true;
    }
    
    /**
     * 获取连接配置文件的路径
     * @return 配置文件路径
     */
    public static String getConnectionsFilePath() {
        return CONNECTIONS_FILE;
    }
    
    /**
     * 获取命令历史文件的路径
     * @return 命令历史文件路径
     */
    public static String getCommandsHistoryFilePath() {
        return COMMANDS_HISTORY_FILE;
    }
}