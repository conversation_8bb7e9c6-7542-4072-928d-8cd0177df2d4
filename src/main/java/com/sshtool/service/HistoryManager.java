package com.sshtool.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.sshtool.model.ConnectionInfo;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

/**
 * 历史记录管理器，负责保存和加载连接历史、命令历史等
 */
public class HistoryManager {
    private static final Logger logger = LogManager.getLogger(HistoryManager.class);
    private static final String APP_DIR = System.getProperty("user.home") + File.separator + ".java-ssh-client";
    private static final String CONNECTIONS_FILE = APP_DIR + File.separator + "connections.json";
    private static final String COMMANDS_HISTORY_FILE = APP_DIR + File.separator + "commands_history.txt";
    private static final String SETTINGS_FILE = APP_DIR + File.separator + "settings.properties";
    
    private ObjectMapper objectMapper;
    
    /**
     * 构造函数，初始化历史管理器
     */
    public HistoryManager() {
        this.objectMapper = new ObjectMapper();
        initializeAppDirectory();
    }
    
    /**
     * 初始化应用程序目录
     */
    private void initializeAppDirectory() {
        try {
            Path appDir = Paths.get(APP_DIR);
            if (!Files.exists(appDir)) {
                Files.createDirectories(appDir);
                logger.info("Created application directory: {}", APP_DIR);
            }
        } catch (IOException e) {
            logger.error("Failed to create application directory: {}", e.getMessage());
        }
    }
    
    /**
     * 保存连接信息
     * @param connectionInfo 连接信息
     */
    public void saveConnection(ConnectionInfo connectionInfo) {
        if (connectionInfo == null || !connectionInfo.isValid()) {
            logger.warn("Invalid connection info, not saving");
            return;
        }
        
        try {
            List<ConnectionInfo> connections = loadConnections();
            
            // 移除已存在的相同连接
            connections.removeIf(conn -> 
                conn.getHostname().equals(connectionInfo.getHostname()) &&
                conn.getPort() == connectionInfo.getPort() &&
                conn.getUsername().equals(connectionInfo.getUsername())
            );
            
            // 更新最后连接时间
            connectionInfo.setLastConnected(System.currentTimeMillis());
            
            // 添加到列表开头
            connections.add(0, connectionInfo);
            
            // 限制保存的连接数量为50个
            if (connections.size() > 50) {
                connections = connections.subList(0, 50);
            }
            
            // 保存到文件
            objectMapper.writeValue(new File(CONNECTIONS_FILE), connections);
            logger.info("Saved connection: {}", connectionInfo.toString());
            
        } catch (IOException e) {
            logger.error("Failed to save connection: {}", e.getMessage());
        }
    }
    
    /**
     * 加载保存的连接信息
     * @return 连接信息列表
     */
    public List<ConnectionInfo> loadConnections() {
        try {
            File file = new File(CONNECTIONS_FILE);
            if (!file.exists()) {
                return new ArrayList<>();
            }
            
            TypeFactory typeFactory = objectMapper.getTypeFactory();
            List<ConnectionInfo> connections = objectMapper.readValue(file, 
                typeFactory.constructCollectionType(List.class, ConnectionInfo.class));
            
            // 按最后连接时间排序
            connections.sort((a, b) -> Long.compare(b.getLastConnected(), a.getLastConnected()));
            
            logger.info("Loaded {} saved connections", connections.size());
            return connections;
            
        } catch (IOException e) {
            logger.error("Failed to load connections: {}", e.getMessage());
            return new ArrayList<>();
        }
    }
    
    /**
     * 删除保存的连接
     * @param connectionInfo 要删除的连接信息
     */
    public void deleteConnection(ConnectionInfo connectionInfo) {
        try {
            List<ConnectionInfo> connections = loadConnections();
            connections.removeIf(conn -> 
                conn.getHostname().equals(connectionInfo.getHostname()) &&
                conn.getPort() == connectionInfo.getPort() &&
                conn.getUsername().equals(connectionInfo.getUsername())
            );
            
            objectMapper.writeValue(new File(CONNECTIONS_FILE), connections);
            logger.info("Deleted connection: {}", connectionInfo.toString());
            
        } catch (IOException e) {
            logger.error("Failed to delete connection: {}", e.getMessage());
        }
    }
    
    /**
     * 加载命令历史
     * @return 命令历史列表
     */
    public List<String> loadCommandHistory() {
        List<String> commands = new ArrayList<>();
        
        try {
            File file = new File(COMMANDS_HISTORY_FILE);
            if (!file.exists()) {
                return commands;
            }
            
            try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    line = line.trim();
                    if (!line.isEmpty()) {
                        commands.add(line);
                    }
                }
            }
            
            logger.info("Loaded {} command history entries", commands.size());
            
        } catch (IOException e) {
            logger.error("Failed to load command history: {}", e.getMessage());
        }
        
        return commands;
    }
    
    /**
     * 保存命令历史
     * @param command 要保存的命令
     */
    public void saveCommandHistory(String command) {
        if (command == null || command.trim().isEmpty()) {
            return;
        }
        
        // 获取已保存的命令历史
        List<String> commands = loadCommandHistory();
        
        // 如果命令已存在，先移除（确保新命令在列表顶部）
        commands.remove(command);
        
        // 添加命令到列表顶部
        commands.add(0, command);
        
        // 限制历史记录大小为100条
        if (commands.size() > 100) {
            commands = commands.subList(0, 100);
        }
        
        // 保存到文件
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(COMMANDS_HISTORY_FILE))) {
            for (String cmd : commands) {
                writer.write(cmd);
                writer.newLine();
            }
        } catch (IOException e) {
            logger.error("Failed to save command history: {}", e.getMessage());
        }
    }
    
    /**
     * 清空命令历史
     */
    public void clearCommandHistory() {
        try {
            File file = new File(COMMANDS_HISTORY_FILE);
            if (file.exists()) {
                file.delete();
            }
            logger.info("Cleared command history");
        } catch (Exception e) {
            logger.error("Failed to clear command history: {}", e.getMessage());
        }
    }
    
    /**
     * 保存应用设置
     * @param settings 设置属性
     */
    public void saveSettings(Properties settings) {
        try (FileOutputStream fos = new FileOutputStream(SETTINGS_FILE)) {
            settings.store(fos, "Java SSH Client Settings");
            logger.info("Settings saved successfully");
        } catch (IOException e) {
            logger.error("Failed to save settings: {}", e.getMessage());
        }
    }
    
    /**
     * 加载应用设置
     * @return 设置属性
     */
    public Properties loadSettings() {
        Properties settings = new Properties();
        
        try {
            File file = new File(SETTINGS_FILE);
            if (file.exists()) {
                try (FileInputStream fis = new FileInputStream(file)) {
                    settings.load(fis);
                }
            }
            logger.info("Settings loaded successfully");
        } catch (IOException e) {
            logger.error("Failed to load settings: {}", e.getMessage());
        }
        
        return settings;
    }
    
    /**
     * 导出连接配置
     * @param exportFile 导出文件
     * @throws IOException 导出异常
     */
    public void exportConnections(File exportFile) throws IOException {
        List<ConnectionInfo> connections = loadConnections();
        
        // 清除敏感信息（密码）
        List<ConnectionInfo> exportConnections = new ArrayList<>();
        for (ConnectionInfo conn : connections) {
            ConnectionInfo exportConn = conn.copy();
            exportConn.setPassword(""); // 不导出密码
            exportConn.setPassphrase(""); // 不导出私钥密码
            exportConnections.add(exportConn);
        }
        
        objectMapper.writerWithDefaultPrettyPrinter().writeValue(exportFile, exportConnections);
        logger.info("Exported {} connections to {}", exportConnections.size(), exportFile.getAbsolutePath());
    }
    
    /**
     * 导入连接配置
     * @param importFile 导入文件
     * @throws IOException 导入异常
     */
    public void importConnections(File importFile) throws IOException {
        TypeFactory typeFactory = objectMapper.getTypeFactory();
        List<ConnectionInfo> importConnections = objectMapper.readValue(importFile, 
            typeFactory.constructCollectionType(List.class, ConnectionInfo.class));
        
        List<ConnectionInfo> existingConnections = loadConnections();
        
        // 合并连接，避免重复
        for (ConnectionInfo importConn : importConnections) {
            boolean exists = existingConnections.stream().anyMatch(conn ->
                conn.getHostname().equals(importConn.getHostname()) &&
                conn.getPort() == importConn.getPort() &&
                conn.getUsername().equals(importConn.getUsername())
            );
            
            if (!exists) {
                existingConnections.add(importConn);
            }
        }
        
        objectMapper.writeValue(new File(CONNECTIONS_FILE), existingConnections);
        logger.info("Imported {} connections from {}", importConnections.size(), importFile.getAbsolutePath());
    }
    
    /**
     * 获取应用数据目录
     * @return 应用数据目录路径
     */
    public String getAppDirectory() {
        return APP_DIR;
    }
}
