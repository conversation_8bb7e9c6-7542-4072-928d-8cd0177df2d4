module com.sshtool {
    requires javafx.controls;
    requires javafx.fxml;
    requires javafx.web;
    requires javafx.graphics;
//    requires com.jcraft.jsch;
    requires com.fasterxml.jackson.databind;
    requires org.apache.logging.log4j;
    requires org.apache.logging.log4j.core;
    requires jsch;

    exports com.sshtool;
    exports com.sshtool.controller;
    opens com.sshtool.controller to javafx.fxml;
    opens com.sshtool to javafx.fxml;
}
