<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<BorderPane xmlns="http://javafx.com/javafx/17" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.sshtool.controller.MainController">
    <top>
        <!-- 菜单栏 -->
        <VBox>
            <MenuBar>
                <Menu text="文件">
                    <MenuItem text="新建连接" onAction="#newConnection"/>
                    <MenuItem text="导入连接" onAction="#importConnections"/>
                    <MenuItem text="导出连接" onAction="#exportConnections"/>
                    <SeparatorMenuItem/>
                    <MenuItem text="设置" onAction="#openSettings"/>
                    <SeparatorMenuItem/>
                    <MenuItem text="退出" onAction="#exitApplication"/>
                </Menu>
                <Menu text="连接">
                    <MenuItem text="连接" onAction="#connect"/>
                    <MenuItem text="断开连接" onAction="#disconnect"/>
                    <MenuItem text="重新连接" onAction="#reconnect"/>
                    <SeparatorMenuItem/>
                    <MenuItem text="测试连接" onAction="#testConnection"/>
                </Menu>
                <Menu text="工具">
                    <MenuItem text="清空终端" onAction="#clearConsole"/>
                    <MenuItem text="清空命令历史" onAction="#clearCommandHistory"/>
                    <SeparatorMenuItem/>
                    <MenuItem text="系统信息" onAction="#showSystemInfo"/>
                </Menu>
                <Menu text="帮助">
                    <MenuItem text="关于" onAction="#showAbout"/>
                    <MenuItem text="使用说明" onAction="#showHelp"/>
                </Menu>
            </MenuBar>
            
            <!-- 工具栏 -->
            <ToolBar>
                <ComboBox fx:id="savedConnections" promptText="选择保存的连接" prefWidth="300"/>
                <Separator orientation="VERTICAL"/>
                <Button text="连接" onAction="#connect" styleClass="primary-button"/>
                <Button text="断开" onAction="#disconnect" styleClass="secondary-button"/>
                <Separator orientation="VERTICAL"/>
                <Label fx:id="connectionStatusLabel" text="未连接" styleClass="status-label"/>
            </ToolBar>
        </VBox>
    </top>

    <center>
        <!-- 主标签页面板 -->
        <TabPane fx:id="mainTabPane" tabClosingPolicy="UNAVAILABLE">
            
            <!-- 连接标签页 -->
            <Tab fx:id="connectionTab" text="连接">
                <ScrollPane fitToWidth="true" fitToHeight="true">
                    <VBox spacing="20" padding="20">
                        <Label text="SSH 连接配置" styleClass="section-title"/>
                        
                        <!-- 连接信息表单 -->
                        <GridPane hgap="15" vgap="15" styleClass="form-grid">
                            <columnConstraints>
                                <ColumnConstraints minWidth="120" prefWidth="120"/>
                                <ColumnConstraints minWidth="200" prefWidth="300"/>
                                <ColumnConstraints minWidth="100" prefWidth="150"/>
                            </columnConstraints>
                            
                            <Label text="主机名:" GridPane.columnIndex="0" GridPane.rowIndex="0"/>
                            <TextField fx:id="hostnameField" promptText="例如: *************" GridPane.columnIndex="1" GridPane.rowIndex="0"/>
                            
                            <Label text="端口:" GridPane.columnIndex="0" GridPane.rowIndex="1"/>
                            <TextField fx:id="portField" text="22" GridPane.columnIndex="1" GridPane.rowIndex="1"/>
                            
                            <Label text="用户名:" GridPane.columnIndex="0" GridPane.rowIndex="2"/>
                            <TextField fx:id="usernameField" promptText="SSH用户名" GridPane.columnIndex="1" GridPane.rowIndex="2"/>
                            
                            <Label text="连接别名:" GridPane.columnIndex="0" GridPane.rowIndex="3"/>
                            <TextField fx:id="nicknameField" promptText="可选，便于识别" GridPane.columnIndex="1" GridPane.rowIndex="3"/>
                        </GridPane>
                        
                        <!-- 认证方式选择 -->
                        <VBox spacing="10">
                            <Label text="认证方式" styleClass="section-subtitle"/>
                            <RadioButton fx:id="passwordAuthRadio" text="密码认证" selected="true"/>
                            <VBox fx:id="passwordAuthBox" spacing="10" padding="0 20 0 20">
                                <HBox spacing="10" alignment="CENTER_LEFT">
                                    <Label text="密码:" minWidth="80"/>
                                    <PasswordField fx:id="passwordField" promptText="SSH密码" prefWidth="300"/>
                                </HBox>
                            </VBox>
                            
                            <RadioButton fx:id="keyAuthRadio" text="私钥认证"/>
                            <VBox fx:id="keyAuthBox" spacing="10" padding="0 20 0 20" disable="true">
                                <HBox spacing="10" alignment="CENTER_LEFT">
                                    <Label text="私钥文件:" minWidth="80"/>
                                    <TextField fx:id="privateKeyField" promptText="选择私钥文件" prefWidth="250"/>
                                    <Button text="浏览" onAction="#browsePrivateKey"/>
                                </HBox>
                                <HBox spacing="10" alignment="CENTER_LEFT">
                                    <Label text="密码短语:" minWidth="80"/>
                                    <PasswordField fx:id="passphraseField" promptText="私钥密码（可选）" prefWidth="300"/>
                                </HBox>
                            </VBox>
                        </VBox>
                        
                        <!-- 操作按钮 -->
                        <HBox spacing="15" alignment="CENTER_LEFT">
                            <CheckBox fx:id="saveConnectionCheck" text="保存此连接" selected="true"/>
                            <Region HBox.hgrow="ALWAYS"/>
                            <Button text="测试连接" onAction="#testConnection" styleClass="secondary-button"/>
                            <Button text="连接" onAction="#connect" styleClass="primary-button"/>
                        </HBox>
                    </VBox>
                </ScrollPane>
            </Tab>
            
            <!-- 终端标签页 -->
            <Tab fx:id="consoleTab" text="终端">
                <VBox>
                    <!-- 终端输出区域 -->
                    <TextArea fx:id="consoleOutput" editable="false" wrapText="true" 
                             styleClass="console-output" VBox.vgrow="ALWAYS"/>
                    
                    <!-- 命令输入区域 -->
                    <HBox spacing="10" padding="10" styleClass="command-input-area">
                        <Label text="$" styleClass="command-prompt"/>
                        <TextField fx:id="commandInput" promptText="输入命令..." 
                                  HBox.hgrow="ALWAYS" styleClass="command-input"/>
                        <Button text="发送" onAction="#sendCommand" styleClass="primary-button"/>
                        <Button text="清空" onAction="#clearConsole" styleClass="secondary-button"/>
                    </HBox>
                </VBox>
            </Tab>
            
            <!-- 文件管理标签页 -->
            <Tab fx:id="fileManagerTab" text="文件管理">
                <SplitPane dividerPositions="0.3">
                    <!-- 左侧：目录树 -->
                    <VBox spacing="5" padding="10">
                        <Label text="目录结构" styleClass="section-subtitle"/>
                        <TreeView fx:id="fileTree" VBox.vgrow="ALWAYS" styleClass="file-tree"/>
                    </VBox>
                    
                    <!-- 右侧：文件列表和操作 -->
                    <VBox spacing="10" padding="10">
                        <!-- 当前路径和操作按钮 -->
                        <HBox spacing="10" alignment="CENTER_LEFT">
                            <Label text="当前路径:"/>
                            <TextField fx:id="currentPathField" editable="false" HBox.hgrow="ALWAYS"/>
                            <Button text="刷新" onAction="#refreshFileList" styleClass="secondary-button"/>
                            <Button text="上传" onAction="#uploadFile" styleClass="primary-button"/>
                            <Button text="新建文件夹" onAction="#createDirectory" styleClass="secondary-button"/>
                        </HBox>
                        
                        <!-- 文件列表 -->
                        <TableView fx:id="fileTable" VBox.vgrow="ALWAYS" styleClass="file-table">
                            <columns>
                                <TableColumn fx:id="fileNameColumn" text="名称" prefWidth="200"/>
                                <TableColumn fx:id="fileSizeColumn" text="大小" prefWidth="100"/>
                                <TableColumn fx:id="fileModifiedColumn" text="修改时间" prefWidth="150"/>
                                <TableColumn fx:id="filePermissionsColumn" text="权限" prefWidth="100"/>
                            </columns>
                        </TableView>
                        
                        <!-- 文件操作按钮 -->
                        <HBox spacing="10" alignment="CENTER_LEFT">
                            <Button text="下载" onAction="#downloadFile" styleClass="primary-button"/>
                            <Button text="删除" onAction="#deleteFile" styleClass="danger-button"/>
                            <Button text="重命名" onAction="#renameFile" styleClass="secondary-button"/>
                            <Button text="属性" onAction="#showFileProperties" styleClass="secondary-button"/>
                        </HBox>
                    </VBox>
                </SplitPane>
            </Tab>
            
            <!-- 系统监控标签页 -->
            <Tab fx:id="monitoringTab" text="系统监控">
                <ScrollPane fitToWidth="true" fitToHeight="true">
                    <VBox spacing="20" padding="20">
                        <Label text="服务器状态监控" styleClass="section-title"/>
                        
                        <!-- 系统概览 -->
                        <GridPane hgap="20" vgap="15" styleClass="monitoring-grid">
                            <columnConstraints>
                                <ColumnConstraints minWidth="150" prefWidth="200"/>
                                <ColumnConstraints minWidth="300" prefWidth="400"/>
                                <ColumnConstraints minWidth="100" prefWidth="150"/>
                            </columnConstraints>
                            
                            <!-- CPU使用率 -->
                            <Label text="CPU 使用率:" GridPane.columnIndex="0" GridPane.rowIndex="0" styleClass="monitor-label"/>
                            <ProgressBar fx:id="cpuUsageBar" prefWidth="300" GridPane.columnIndex="1" GridPane.rowIndex="0" styleClass="cpu-progress"/>
                            <Label fx:id="cpuUsageLabel" text="0%" GridPane.columnIndex="2" GridPane.rowIndex="0" styleClass="monitor-value"/>
                            
                            <!-- 内存使用率 -->
                            <Label text="内存使用率:" GridPane.columnIndex="0" GridPane.rowIndex="1" styleClass="monitor-label"/>
                            <ProgressBar fx:id="memoryUsageBar" prefWidth="300" GridPane.columnIndex="1" GridPane.rowIndex="1" styleClass="memory-progress"/>
                            <Label fx:id="memoryUsageLabel" text="0%" GridPane.columnIndex="2" GridPane.rowIndex="1" styleClass="monitor-value"/>
                            
                            <!-- 磁盘使用率 -->
                            <Label text="磁盘使用率:" GridPane.columnIndex="0" GridPane.rowIndex="2" styleClass="monitor-label"/>
                            <ProgressBar fx:id="diskUsageBar" prefWidth="300" GridPane.columnIndex="1" GridPane.rowIndex="2" styleClass="disk-progress"/>
                            <Label fx:id="diskUsageLabel" text="0%" GridPane.columnIndex="2" GridPane.rowIndex="2" styleClass="monitor-value"/>
                            
                            <!-- 网络流量 -->
                            <Label text="网络接收:" GridPane.columnIndex="0" GridPane.rowIndex="3" styleClass="monitor-label"/>
                            <Label fx:id="networkRxLabel" text="0 MB/s" GridPane.columnIndex="1" GridPane.rowIndex="3" styleClass="network-value"/>
                            
                            <Label text="网络发送:" GridPane.columnIndex="0" GridPane.rowIndex="4" styleClass="monitor-label"/>
                            <Label fx:id="networkTxLabel" text="0 MB/s" GridPane.columnIndex="1" GridPane.rowIndex="4" styleClass="network-value"/>
                            
                            <!-- 系统信息 -->
                            <Label text="运行时间:" GridPane.columnIndex="0" GridPane.rowIndex="5" styleClass="monitor-label"/>
                            <Label fx:id="uptimeLabel" text="0分钟" GridPane.columnIndex="1" GridPane.rowIndex="5" styleClass="monitor-value"/>
                            
                            <Label text="进程数:" GridPane.columnIndex="0" GridPane.rowIndex="6" styleClass="monitor-label"/>
                            <Label fx:id="processCountLabel" text="0" GridPane.columnIndex="1" GridPane.rowIndex="6" styleClass="monitor-value"/>
                            
                            <Label text="系统负载:" GridPane.columnIndex="0" GridPane.rowIndex="7" styleClass="monitor-label"/>
                            <Label fx:id="loadAverageLabel" text="0.00" GridPane.columnIndex="1" GridPane.rowIndex="7" styleClass="monitor-value"/>
                        </GridPane>
                        
                        <!-- 监控控制 -->
                        <HBox spacing="15" alignment="CENTER_LEFT">
                            <Button fx:id="startMonitoringButton" text="开始监控" onAction="#startMonitoring" styleClass="primary-button"/>
                            <Button fx:id="stopMonitoringButton" text="停止监控" onAction="#stopMonitoring" styleClass="secondary-button" disable="true"/>
                            <CheckBox fx:id="autoStartMonitoringCheck" text="连接时自动开始监控" selected="true"/>
                        </HBox>
                    </VBox>
                </ScrollPane>
            </Tab>
        </TabPane>
    </center>

    <bottom>
        <!-- 状态栏 -->
        <HBox spacing="10" padding="5 10" styleClass="status-bar">
            <Label fx:id="statusLabel" text="就绪" styleClass="status-text"/>
            <Region HBox.hgrow="ALWAYS"/>
            <Label fx:id="connectionInfoLabel" text="" styleClass="connection-info"/>
            <Separator orientation="VERTICAL"/>
            <Label fx:id="timeLabel" text="" styleClass="time-label"/>
        </HBox>
    </bottom>
</BorderPane>
