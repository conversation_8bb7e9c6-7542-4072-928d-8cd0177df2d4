<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<BorderPane xmlns="http://javafx.com/javafx/17" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.sshtool.MainController">
    <top>
        <MenuBar>
            <Menu text="File">
                <MenuItem text="New Terminal" onAction="#openNewTerminal"/>
                <MenuItem text="Settings" onAction="#openSettings"/>
                <SeparatorMenuItem/>
                <MenuItem text="Exit" onAction="#disconnect"/>
            </Menu>
            <Menu text="Connection">
                <MenuItem text="Connect" onAction="#connect"/>
                <MenuItem text="Disconnect" onAction="#disconnect"/>
            </Menu>
            <Menu text="Help">
                <MenuItem text="About"/>
                <MenuItem text="Documentation"/>
            </Menu>
        </MenuBar>
    </top>
    
    <center>
        <TabPane fx:id="mainTabPane">
            <!-- Connection Tab -->
            <Tab fx:id="connectionTab" text="Connection">
                <GridPane hgap="10" vgap="10" padding="15">
                    <Label text="Saved Connections:" GridPane.columnIndex="0" GridPane.rowIndex="0"/>
                    <ComboBox fx:id="savedConnections" GridPane.columnIndex="1" GridPane.rowIndex="0" GridPane.columnSpan="2">
                        <buttonCell>
                            <ListCell>
                                <fx:script>
                                    function updateItem(item, empty) {
                                        if (empty || item == null) {
                                            text = "";
                                        } else {
                                            text = item.toString();
                                        }
                                    }
                                </fx:script>
                            </ListCell>
                        </buttonCell>
                    </ComboBox>
                    
                    <Label text="Hostname:" GridPane.columnIndex="0" GridPane.rowIndex="1"/>
                    <TextField fx:id="hostnameField" GridPane.columnIndex="1" GridPane.rowIndex="1" GridPane.columnSpan="2"/>
                    
                    <Label text="Port:" GridPane.columnIndex="0" GridPane.rowIndex="2"/>
                    <TextField fx:id="portField" GridPane.columnIndex="1" GridPane.rowIndex="2" promptText="22"/>
                    
                    <Label text="Username:" GridPane.columnIndex="0" GridPane.rowIndex="3"/>
                    <TextField fx:id="usernameField" GridPane.columnIndex="1" GridPane.rowIndex="3" GridPane.columnSpan="2"/>
                    
                    <Label text="Password:" GridPane.columnIndex="0" GridPane.rowIndex="4"/>
                    <PasswordField fx:id="passwordField" GridPane.columnIndex="1" GridPane.rowIndex="4" GridPane.columnSpan="2"/>
                    
                    <Label text="Private Key:" GridPane.columnIndex="0" GridPane.rowIndex="5"/>
                    <TextField fx:id="privateKeyField" GridPane.columnIndex="1" GridPane.rowIndex="5"/>
                    <Button text="Browse..." GridPane.columnIndex="2" GridPane.rowIndex="5"/>
                    
                    <Label text="Passphrase:" GridPane.columnIndex="0" GridPane.rowIndex="6"/>
                    <PasswordField fx:id="passphraseField" GridPane.columnIndex="1" GridPane.rowIndex="6" GridPane.columnSpan="2"/>
                    
                    <HBox spacing="10" GridPane.columnIndex="1" GridPane.rowIndex="7" GridPane.columnSpan="2">
                        <CheckBox fx:id="saveConnectionCheck" text="Save connection" selected="true"/>
                    </HBox>
                    
                    <HBox spacing="10" GridPane.columnIndex="1" GridPane.rowIndex="8" GridPane.columnSpan="2" alignment="CENTER_RIGHT">
                        <Button text="Connect" onAction="#connect" styleClass="primary-button"/>
                        <Button text="Disconnect" onAction="#disconnect"/>
                    </HBox>
                </GridPane>
            </Tab>
            
            <!-- Console Tab -->
            <Tab fx:id="consoleTab" text="Console" disable="true">
                <VBox spacing="5" padding="5">
                    <TextArea fx:id="consoleOutput" editable="false" wrapText="true" VBox.vgrow="ALWAYS">
                        <font>
                            <Font name="Consolas" size="14"/>
                        </font>
                    </TextArea>
                    <TextField fx:id="commandInput" promptText="Enter command..."/>
                </VBox>
            </Tab>
            
            <!-- File Manager Tab -->
            <Tab fx:id="fileManagerTab" text="File Manager" disable="true">
                <BorderPane>
                    <left>
                        <VBox padding="5" spacing="5" minWidth="250">
                            <Label text="File System" font="14px Arial"/>
                            <TreeView fx:id="fileTree" VBox.vgrow="ALWAYS"/>
                            <HBox spacing="5" padding="5">
                                <Button text="Download" onAction="#downloadFile"/>
                                <Button text="Upload" onAction="#uploadFile"/>
                            </HBox>
                        </VBox>
                    </left>
                    <center>
                        <VBox padding="5" spacing="5">
                            <Label text="File List" font="14px Arial"/>
                            <ListView fx:id="fileList" VBox.vgrow="ALWAYS"/>
                        </VBox>
                    </center>
                </BorderPane>
            </Tab>
            
            <!-- Monitoring Tab -->
            <Tab fx:id="monitoringTab" text="Monitoring" disable="true">
                <VBox padding="15" spacing="15">
                    <Label text="Server Monitoring" font="20px Arial"/>
                    
                    <VBox spacing="10">
                        <HBox spacing="10" alignment="CENTER_LEFT">
                            <Label fx:id="cpuUsageLabel" text="CPU: 0%" prefWidth="100"/>
                            <ProgressBar fx:id="cpuUsageBar" progress="0" prefWidth="400"/>
                        </HBox>
                        
                        <HBox spacing="10" alignment="CENTER_LEFT">
                            <Label fx:id="memoryUsageLabel" text="Memory: 0%" prefWidth="100"/>
                            <ProgressBar fx:id="memoryUsageBar" progress="0" prefWidth="400"/>
                        </HBox>
                        
                        <HBox spacing="10" alignment="CENTER_LEFT">
                            <Label fx:id="diskUsageLabel" text="Disk: 0%" prefWidth="100"/>
                            <ProgressBar fx:id="diskUsageBar" progress="0" prefWidth="400"/>
                        </HBox>
                    </VBox>
                    
                    <VBox spacing="10" marginTop="20">
                        <Label text="Network Traffic" font="16px Arial"/>
                        <GridPane hgap="10" vgap="10">
                            <Label text="Download Rate:" GridPane.columnIndex="0" GridPane.rowIndex="0"/>
                            <Label fx:id="networkRxLabel" text="Rx: 0.00 MB/s" GridPane.columnIndex="1" GridPane.rowIndex="0"/>
                            
                            <Label text="Upload Rate:" GridPane.columnIndex="0" GridPane.rowIndex="1"/>
                            <Label fx:id="networkTxLabel" text="Tx: 0.00 MB/s" GridPane.columnIndex="1" GridPane.rowIndex="1"/>
                        </GridPane>
                    </VBox>
                </VBox>
            </Tab>
        </TabPane>
    </center>
    
    <stylesheets>
        <URL value="css/styles.css"/>
    </stylesheets>
</BorderPane>