/* 全局样式 */
* {
    -fx-font-family: "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
}

/* 主界面样式 */
BorderPane {
    -fx-background-color: #f5f5f5;
}

/* 菜单样式 */
MenuBar {
    -fx-background-color: #3f51b5;
    -fx-text-fill: white;
}

Menu {
    -fx-background-color: transparent;
    -fx-text-fill: white;
    -fx-padding: 8 16;
    -fx-cursor: hand;
}

Menu:hover {
    -fx-background-color: #303f9f;
}

MenuItem {
    -fx-background-color: white;
    -fx-text-fill: #333;
    -fx-padding: 8 16;
}

MenuItem:hover {
    -fx-background-color: #e8eaf6;
}

/* 标签页样式 */
TabPane {
    -fx-background-color: white;
    -fx-border-color: #e0e0e0;
    -fx-border-width: 1 0 0 0;
}

Tab {
    -fx-background-color: #f5f5f5;
    -fx-padding: 10 20;
    -fx-text-fill: #616161;
    -fx-border-color: #e0e0e0;
    -fx-border-width: 0 1 0 0;
}

Tab:hover {
    -fx-background-color: #eeeeee;
}

Tab:selected {
    -fx-background-color: white;
    -fx-text-fill: #3f51b5;
    -fx-border-color: #3f51b5 #e0e0e0 #ffffff 0;
    -fx-border-width: 3 1 0 0;
}

/* 按钮样式 */
Button {
    -fx-background-color: #f5f5f5;
    -fx-text-fill: #333;
    -fx-padding: 8 16;
    -fx-border-radius: 4;
    -fx-background-radius: 4;
    -fx-cursor: hand;
    -fx-font-weight: 500;
}

Button:hover {
    -fx-background-color: #e0e0e0;
}

Button:disabled {
    -fx-background-color: #f5f5f5;
    -fx-text-fill: #9e9e9e;
    -fx-cursor: default;
}

.primary-button {
    -fx-background-color: #3f51b5;
    -fx-text-fill: white;
}

.primary-button:hover {
    -fx-background-color: #303f9f;
}

/* 文本字段样式 */
TextField, PasswordField {
    -fx-background-color: white;
    -fx-border-color: #e0e0e0;
    -fx-border-radius: 4;
    -fx-background-radius: 4;
    -fx-padding: 8 12;
    -fx-font-size: 14;
}

TextField:focused, PasswordField:focused {
    -fx-border-color: #3f51b5;
    -fx-border-width: 2;
}

/* 文本区域样式 */
TextArea {
    -fx-background-color: white;
    -fx-border-color: #e0e0e0;
    -fx-border-radius: 4;
    -fx-background-radius: 4;
    -fx-padding: 10;
}

TextArea:focused {
    -fx-border-color: #3f51b5;
    -fx-border-width: 2;
}

/* 进度条样式 */
ProgressBar {
    -fx-background-color: #e0e0e0;
    -fx-background-radius: 10;
    -fx-indeterminate-bar-length: 60;
    -fx-indeterminate-bar-escape: true;
    -fx-indeterminate-bar-animation-time: 2;
}

ProgressBar .track {
    -fx-background-color: #e0e0e0;
    -fx-background-radius: 10;
    -fx-border-radius: 10;
}

ProgressBar .bar {
    -fx-background-color: linear-gradient(to right, #4caf50, #45a049);
    -fx-background-radius: 10;
    -fx-padding: 0;
}

/* 树视图和列表视图样式 */
TreeView, ListView {
    -fx-background-color: white;
    -fx-border-color: #e0e0e0;
    -fx-border-radius: 4;
    -fx-background-radius: 4;
}

TreeView .tree-cell, ListView .list-cell {
    -fx-padding: 8 12;
    -fx-text-fill: #333;
}

TreeView .tree-cell:hover, ListView .list-cell:hover {
    -fx-background-color: #e8eaf6;
}

TreeView .tree-cell:selected, ListView .list-cell:selected {
    -fx-background-color: #3f51b5;
    -fx-text-fill: white;
}

/* 标签样式 */
Label {
    -fx-text-fill: #333;
    -fx-font-size: 14;
}

/* 网格面板样式 */
GridPane {
    -fx-background-color: white;
}

/* 组合框样式 */
ComboBox {
    -fx-background-color: white;
    -fx-border-color: #e0e0e0;
    -fx-border-radius: 4;
    -fx-background-radius: 4;
    -fx-padding: 8 12;
}

ComboBox:focused {
    -fx-border-color: #3f51b5;
    -fx-border-width: 2;
}

/* 复选框样式 */
CheckBox {
    -fx-text-fill: #333;
    -fx-font-size: 14;
}

/* 分隔线样式 */
Separator {
    -fx-background-color: #e0e0e0;
}

/* 警告对话框样式 */
.alert {
    -fx-background-color: white;
    -fx-border-color: #e0e0e0;
    -fx-border-radius: 4;
    -fx-background-radius: 4;
}

/* 工具提示样式 */
Tooltip {
    -fx-background-color: rgba(0, 0, 0, 0.9);
    -fx-text-fill: white;
    -fx-padding: 8 12;
    -fx-border-radius: 4;
    -fx-background-radius: 4;
    -fx-font-size: 12;
}

/* 滚动条样式 */
.scroll-bar {
    -fx-background-color: #f5f5f5;
    -fx-width: 12;
}

.scroll-bar .thumb {
    -fx-background-color: #9e9e9e;
    -fx-background-radius: 6;
}

.scroll-bar .thumb:hover {
    -fx-background-color: #757575;
}

.scroll-bar .track {
    -fx-background-color: #f5f5f5;
}

/* 选项卡面板样式优化 */
.tab-header-background {
    -fx-background-color: #f5f5f5;
}

/* 控制台文本样式 */
.consolas-font {
    -fx-font-family: "Consolas", "Monaco", monospace;
}