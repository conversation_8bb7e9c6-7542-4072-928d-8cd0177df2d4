<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<BorderPane xmlns="http://javafx.com/javafx/17" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.sshtool.controller.SettingsController">
    <center>
        <TabPane>
            <!-- 常规设置标签页 -->
            <Tab text="常规">
                <VBox padding="20" spacing="20">
                    <Label text="常规设置" styleClass="section-title"/>
                    
                    <!-- 主题设置 -->
                    <VBox spacing="10">
                        <Label text="界面主题" styleClass="section-subtitle"/>
                        <ComboBox fx:id="themeComboBox" prefWidth="200">
                            <items>
                                <String fx:value="浅色主题"/>
                                <String fx:value="深色主题"/>
                                <String fx:value="系统默认"/>
                            </items>
                        </ComboBox>
                    </VBox>
                    
                    <!-- 语言设置 -->
                    <VBox spacing="10">
                        <Label text="界面语言" styleClass="section-subtitle"/>
                        <ComboBox fx:id="languageComboBox" prefWidth="200">
                            <items>
                                <String fx:value="简体中文"/>
                                <String fx:value="English"/>
                            </items>
                        </ComboBox>
                    </VBox>
                    
                    <!-- 启动设置 -->
                    <VBox spacing="10">
                        <Label text="启动选项" styleClass="section-subtitle"/>
                        <CheckBox fx:id="autoConnectLastCheck" text="启动时自动连接到上次使用的服务器"/>
                        <CheckBox fx:id="minimizeToTrayCheck" text="最小化到系统托盘"/>
                        <CheckBox fx:id="checkUpdatesCheck" text="启动时检查更新" selected="true"/>
                    </VBox>
                </VBox>
            </Tab>
            
            <!-- 终端设置标签页 -->
            <Tab text="终端">
                <VBox padding="20" spacing="20">
                    <Label text="终端设置" styleClass="section-title"/>
                    
                    <!-- 字体设置 -->
                    <VBox spacing="10">
                        <Label text="字体设置" styleClass="section-subtitle"/>
                        <HBox spacing="15" alignment="CENTER_LEFT">
                            <Label text="字体:" minWidth="80"/>
                            <ComboBox fx:id="fontFamilyComboBox" prefWidth="200">
                                <items>
                                    <String fx:value="Consolas"/>
                                    <String fx:value="Monaco"/>
                                    <String fx:value="Courier New"/>
                                    <String fx:value="DejaVu Sans Mono"/>
                                    <String fx:value="Source Code Pro"/>
                                </items>
                            </ComboBox>
                        </HBox>
                        <HBox spacing="15" alignment="CENTER_LEFT">
                            <Label text="字体大小:" minWidth="80"/>
                            <Slider fx:id="fontSizeSlider" min="8" max="24" value="12" 
                                   majorTickUnit="4" minorTickCount="3" showTickLabels="true" 
                                   showTickMarks="true" prefWidth="200"/>
                            <Label fx:id="fontSizeLabel" text="12px" minWidth="40"/>
                        </HBox>
                    </VBox>
                    
                    <!-- 颜色设置 -->
                    <VBox spacing="10">
                        <Label text="颜色方案" styleClass="section-subtitle"/>
                        <ComboBox fx:id="colorSchemeComboBox" prefWidth="200">
                            <items>
                                <String fx:value="默认"/>
                                <String fx:value="Solarized Dark"/>
                                <String fx:value="Solarized Light"/>
                                <String fx:value="Monokai"/>
                                <String fx:value="Tomorrow Night"/>
                            </items>
                        </ComboBox>
                    </VBox>
                    
                    <!-- 终端行为 -->
                    <VBox spacing="10">
                        <Label text="终端行为" styleClass="section-subtitle"/>
                        <CheckBox fx:id="saveCommandHistoryCheck" text="保存命令历史" selected="true"/>
                        <HBox spacing="15" alignment="CENTER_LEFT">
                            <Label text="历史记录条数:" minWidth="120"/>
                            <Spinner fx:id="historyLimitSpinner" min="50" max="1000" initialValue="100" 
                                    prefWidth="100" editable="true"/>
                        </HBox>
                        <CheckBox fx:id="autoScrollCheck" text="自动滚动到底部" selected="true"/>
                        <CheckBox fx:id="wordWrapCheck" text="自动换行" selected="true"/>
                    </VBox>
                </VBox>
            </Tab>
            
            <!-- 连接设置标签页 -->
            <Tab text="连接">
                <VBox padding="20" spacing="20">
                    <Label text="连接设置" styleClass="section-title"/>
                    
                    <!-- 超时设置 -->
                    <VBox spacing="10">
                        <Label text="超时设置" styleClass="section-subtitle"/>
                        <HBox spacing="15" alignment="CENTER_LEFT">
                            <Label text="连接超时:" minWidth="100"/>
                            <Spinner fx:id="connectionTimeoutSpinner" min="5" max="120" initialValue="30" 
                                    prefWidth="100" editable="true"/>
                            <Label text="秒"/>
                        </HBox>
                        <HBox spacing="15" alignment="CENTER_LEFT">
                            <Label text="读取超时:" minWidth="100"/>
                            <Spinner fx:id="readTimeoutSpinner" min="10" max="300" initialValue="60" 
                                    prefWidth="100" editable="true"/>
                            <Label text="秒"/>
                        </HBox>
                    </VBox>
                    
                    <!-- SSH设置 -->
                    <VBox spacing="10">
                        <Label text="SSH设置" styleClass="section-subtitle"/>
                        <CheckBox fx:id="compressionCheck" text="启用压缩"/>
                        <HBox spacing="15" alignment="CENTER_LEFT">
                            <Label text="压缩级别:" minWidth="100"/>
                            <Spinner fx:id="compressionLevelSpinner" min="1" max="9" initialValue="6" 
                                    prefWidth="100" editable="true" disable="true"/>
                        </HBox>
                        <CheckBox fx:id="keepAliveCheck" text="启用Keep-Alive" selected="true"/>
                        <HBox spacing="15" alignment="CENTER_LEFT">
                            <Label text="Keep-Alive间隔:" minWidth="120"/>
                            <Spinner fx:id="keepAliveIntervalSpinner" min="30" max="300" initialValue="60" 
                                    prefWidth="100" editable="true"/>
                            <Label text="秒"/>
                        </HBox>
                    </VBox>
                    
                    <!-- 安全设置 -->
                    <VBox spacing="10">
                        <Label text="安全设置" styleClass="section-subtitle"/>
                        <CheckBox fx:id="strictHostKeyCheck" text="严格主机密钥检查"/>
                        <CheckBox fx:id="savePasswordCheck" text="保存密码（不推荐）"/>
                        <CheckBox fx:id="autoCloseCheck" text="空闲时自动断开连接"/>
                        <HBox spacing="15" alignment="CENTER_LEFT">
                            <Label text="空闲超时:" minWidth="100"/>
                            <Spinner fx:id="idleTimeoutSpinner" min="5" max="120" initialValue="30" 
                                    prefWidth="100" editable="true"/>
                            <Label text="分钟"/>
                        </HBox>
                    </VBox>
                </VBox>
            </Tab>
            
            <!-- 文件传输设置标签页 -->
            <Tab text="文件传输">
                <VBox padding="20" spacing="20">
                    <Label text="文件传输设置" styleClass="section-title"/>
                    
                    <!-- 传输设置 -->
                    <VBox spacing="10">
                        <Label text="传输设置" styleClass="section-subtitle"/>
                        <HBox spacing="15" alignment="CENTER_LEFT">
                            <Label text="默认下载目录:" minWidth="120"/>
                            <TextField fx:id="downloadDirField" prefWidth="300" 
                                      text="${user.home}/Downloads"/>
                            <Button text="浏览" onAction="#browseDownloadDir"/>
                        </HBox>
                        <CheckBox fx:id="overwriteFilesCheck" text="覆盖已存在的文件"/>
                        <CheckBox fx:id="preserveTimestampCheck" text="保持文件时间戳" selected="true"/>
                        <CheckBox fx:id="showTransferProgressCheck" text="显示传输进度" selected="true"/>
                    </VBox>
                    
                    <!-- 性能设置 -->
                    <VBox spacing="10">
                        <Label text="性能设置" styleClass="section-subtitle"/>
                        <HBox spacing="15" alignment="CENTER_LEFT">
                            <Label text="并发传输数:" minWidth="120"/>
                            <Spinner fx:id="concurrentTransfersSpinner" min="1" max="10" initialValue="3" 
                                    prefWidth="100" editable="true"/>
                        </HBox>
                        <HBox spacing="15" alignment="CENTER_LEFT">
                            <Label text="缓冲区大小:" minWidth="120"/>
                            <ComboBox fx:id="bufferSizeComboBox" prefWidth="150">
                                <items>
                                    <String fx:value="32KB"/>
                                    <String fx:value="64KB"/>
                                    <String fx:value="128KB"/>
                                    <String fx:value="256KB"/>
                                    <String fx:value="512KB"/>
                                </items>
                            </ComboBox>
                        </HBox>
                    </VBox>
                </VBox>
            </Tab>
            
            <!-- 监控设置标签页 -->
            <Tab text="监控">
                <VBox padding="20" spacing="20">
                    <Label text="监控设置" styleClass="section-title"/>
                    
                    <!-- 监控间隔 -->
                    <VBox spacing="10">
                        <Label text="监控间隔" styleClass="section-subtitle"/>
                        <HBox spacing="15" alignment="CENTER_LEFT">
                            <Label text="更新间隔:" minWidth="100"/>
                            <Spinner fx:id="monitoringIntervalSpinner" min="1" max="60" initialValue="2" 
                                    prefWidth="100" editable="true"/>
                            <Label text="秒"/>
                        </HBox>
                    </VBox>
                    
                    <!-- 监控项目 -->
                    <VBox spacing="10">
                        <Label text="监控项目" styleClass="section-subtitle"/>
                        <CheckBox fx:id="monitorCpuCheck" text="CPU使用率" selected="true"/>
                        <CheckBox fx:id="monitorMemoryCheck" text="内存使用率" selected="true"/>
                        <CheckBox fx:id="monitorDiskCheck" text="磁盘使用率" selected="true"/>
                        <CheckBox fx:id="monitorNetworkCheck" text="网络流量" selected="true"/>
                        <CheckBox fx:id="monitorProcessCheck" text="进程信息" selected="true"/>
                    </VBox>
                    
                    <!-- 警告设置 -->
                    <VBox spacing="10">
                        <Label text="警告设置" styleClass="section-subtitle"/>
                        <CheckBox fx:id="enableAlertsCheck" text="启用资源使用警告"/>
                        <HBox spacing="15" alignment="CENTER_LEFT">
                            <Label text="CPU警告阈值:" minWidth="120"/>
                            <Spinner fx:id="cpuAlertThresholdSpinner" min="50" max="95" initialValue="80" 
                                    prefWidth="100" editable="true"/>
                            <Label text="%"/>
                        </HBox>
                        <HBox spacing="15" alignment="CENTER_LEFT">
                            <Label text="内存警告阈值:" minWidth="120"/>
                            <Spinner fx:id="memoryAlertThresholdSpinner" min="50" max="95" initialValue="85" 
                                    prefWidth="100" editable="true"/>
                            <Label text="%"/>
                        </HBox>
                        <HBox spacing="15" alignment="CENTER_LEFT">
                            <Label text="磁盘警告阈值:" minWidth="120"/>
                            <Spinner fx:id="diskAlertThresholdSpinner" min="70" max="98" initialValue="90" 
                                    prefWidth="100" editable="true"/>
                            <Label text="%"/>
                        </HBox>
                    </VBox>
                </VBox>
            </Tab>
        </TabPane>
    </center>

    <bottom>
        <!-- 按钮栏 -->
        <HBox spacing="10" padding="15" alignment="CENTER_RIGHT" styleClass="button-bar">
            <Button text="恢复默认" onAction="#restoreDefaults" styleClass="secondary-button"/>
            <Button text="应用" onAction="#applySettings" styleClass="primary-button"/>
            <Button text="确定" onAction="#saveAndClose" styleClass="primary-button"/>
            <Button text="取消" onAction="#cancel" styleClass="secondary-button"/>
        </HBox>
    </bottom>
</BorderPane>
