<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<BorderPane xmlns="http://javafx.com/javafx/17" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.sshtool.SettingsController">
    <center>
        <TabPane>
            <!-- General Settings Tab -->
            <Tab text="General">
                <VBox padding="15" spacing="15">
                    <Label text="General Settings" font="20px Arial"/>
                    
                    <GridPane hgap="10" vgap="10">
                        <Label text="Theme:" GridPane.columnIndex="0" GridPane.rowIndex="0"/>
                        <ComboBox fx:id="themeComboBox" GridPane.columnIndex="1" GridPane.rowIndex="0">
                            <items>
                                <FXCollections fx:factory="observableArrayList">
                                    <String fx:value="Light"/>
                                    <String fx:value="Dark"/>
                                </FXCollections>
                            </items>
                        </ComboBox>
                        
                        <Label text="Font Size:" GridPane.columnIndex="0" GridPane.rowIndex="1"/>
                        <Slider fx:id="fontSizeSlider" min="10" max="20" value="14" showTickLabels="true" showTickMarks="true" majorTickUnit="2" minorTickCount="1" GridPane.columnIndex="1" GridPane.rowIndex="1"/>
                        <Label fx:id="fontSizeLabel" text="14px" GridPane.columnIndex="2" GridPane.rowIndex="1"/>
                        
                        <Label text="Console History Size:" GridPane.columnIndex="0" GridPane.rowIndex="2"/>
                        <Spinner fx:id="historySizeSpinner" GridPane.columnIndex="1" GridPane.rowIndex="2">
                            <valueFactory>
                                <SpinnerValueFactory.IntegerSpinnerValueFactory min="10" max="1000" initialValue="100"/>
                            </valueFactory>
                        </Spinner>
                    </GridPane>
                </VBox>
            </Tab>
            
            <!-- Connection Settings Tab -->
            <Tab text="Connection">
                <VBox padding="15" spacing="15">
                    <Label text="Connection Settings" font="20px Arial"/>
                    
                    <GridPane hgap="10" vgap="10">
                        <Label text="Default Port:" GridPane.columnIndex="0" GridPane.rowIndex="0"/>
                        <TextField fx:id="defaultPortField" text="22" GridPane.columnIndex="1" GridPane.rowIndex="0"/>
                        
                        <Label text="Connection Timeout (seconds):" GridPane.columnIndex="0" GridPane.rowIndex="1"/>
                        <Spinner fx:id="timeoutSpinner" GridPane.columnIndex="1" GridPane.rowIndex="1">
                            <valueFactory>
                                <SpinnerValueFactory.IntegerSpinnerValueFactory min="5" max="300" initialValue="30"/>
                            </valueFactory>
                        </Spinner>
                        
                        <Label text="Strict Host Key Checking:" GridPane.columnIndex="0" GridPane.rowIndex="2"/>
                        <ComboBox fx:id="hostKeyCheckingComboBox" GridPane.columnIndex="1" GridPane.rowIndex="2">
                            <items>
                                <FXCollections fx:factory="observableArrayList">
                                    <String fx:value="no"/>
                                    <String fx:value="ask"/>
                                    <String fx:value="yes"/>
                                </FXCollections>
                            </items>
                            <value>
                                <String fx:value="no"/>
                            </value>
                        </ComboBox>
                        
                        <Label text="Enable Compression:" GridPane.columnIndex="0" GridPane.rowIndex="3"/>
                        <CheckBox fx:id="compressionCheck" selected="true" GridPane.columnIndex="1" GridPane.rowIndex="3"/>
                        
                        <Label text="Compression Level:" GridPane.columnIndex="0" GridPane.rowIndex="4"/>
                        <Spinner fx:id="compressionLevelSpinner" GridPane.columnIndex="1" GridPane.rowIndex="4" disable="${!compressionCheck.selected}">
                            <valueFactory>
                                <SpinnerValueFactory.IntegerSpinnerValueFactory min="1" max="9" initialValue="9"/>
                            </valueFactory>
                        </Spinner>
                    </GridPane>
                </VBox>
            </Tab>
            
            <!-- Monitoring Settings Tab -->
            <Tab text="Monitoring">
                <VBox padding="15" spacing="15">
                    <Label text="Monitoring Settings" font="20px Arial"/>
                    
                    <GridPane hgap="10" vgap="10">
                        <Label text="Update Interval (seconds):" GridPane.columnIndex="0" GridPane.rowIndex="0"/>
                        <Spinner fx:id="monitoringIntervalSpinner" GridPane.columnIndex="1" GridPane.rowIndex="0">
                            <valueFactory>
                                <SpinnerValueFactory.IntegerSpinnerValueFactory min="1" max="30" initialValue="2"/>
                            </valueFactory>
                        </Spinner>
                        
                        <Label text="Enable CPU Monitoring:" GridPane.columnIndex="0" GridPane.rowIndex="1"/>
                        <CheckBox fx:id="cpuMonitorCheck" selected="true" GridPane.columnIndex="1" GridPane.rowIndex="1"/>
                        
                        <Label text="Enable Memory Monitoring:" GridPane.columnIndex="0" GridPane.rowIndex="2"/>
                        <CheckBox fx:id="memoryMonitorCheck" selected="true" GridPane.columnIndex="1" GridPane.rowIndex="2"/>
                        
                        <Label text="Enable Disk Monitoring:" GridPane.columnIndex="0" GridPane.rowIndex="3"/>
                        <CheckBox fx:id="diskMonitorCheck" selected="true" GridPane.columnIndex="1" GridPane.rowIndex="3"/>
                        
                        <Label text="Enable Network Monitoring:" GridPane.columnIndex="0" GridPane.rowIndex="4"/>
                        <CheckBox fx:id="networkMonitorCheck" selected="true" GridPane.columnIndex="1" GridPane.rowIndex="4"/>
                    </GridPane>
                </VBox>
            </Tab>
        </TabPane>
    </center>
    
    <bottom>
        <HBox padding="15" spacing="10" alignment="CENTER_RIGHT">
            <Button text="Apply" onAction="#applySettings" styleClass="primary-button"/>
            <Button text="OK" onAction="#saveSettings" styleClass="primary-button"/>
            <Button text="Cancel" onAction="#cancelSettings"/>
        </HBox>
    </bottom>
    
    <stylesheets>
        <URL value="css/styles.css"/>
    </stylesheets>
</BorderPane>