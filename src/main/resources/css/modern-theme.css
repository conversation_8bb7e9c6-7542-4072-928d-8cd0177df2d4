/* 现代化SSH客户端样式 */

/* 根样式 */
.root {
    -fx-font-family: "Segoe UI", "Microsoft YaHei", "PingFang SC", sans-serif;
    -fx-font-size: 13px;
    -fx-base: #f4f4f4;
    -fx-background: #ffffff;
    -fx-control-inner-background: #ffffff;
    -fx-accent: #0078d4;
    -fx-default-button: #0078d4;
    -fx-focus-color: #0078d4;
    -fx-faint-focus-color: #0078d422;
}

/* 菜单栏样式 */
.menu-bar {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #e9ecef;
    -fx-border-width: 0 0 1 0;
}

.menu-bar .menu {
    -fx-padding: 5 10;
}

.menu-bar .menu:hover {
    -fx-background-color: #e9ecef;
}

/* 工具栏样式 */
.tool-bar {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #e9ecef;
    -fx-border-width: 0 0 1 0;
    -fx-padding: 8 10;
}

/* 按钮样式 */
.button {
    -fx-background-color: #ffffff;
    -fx-border-color: #d1d5db;
    -fx-border-width: 1;
    -fx-border-radius: 6;
    -fx-background-radius: 6;
    -fx-padding: 6 12;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 2, 0, 0, 1);
}

.button:hover {
    -fx-background-color: #f9fafb;
    -fx-border-color: #9ca3af;
}

.button:pressed {
    -fx-background-color: #f3f4f6;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 1, 0, 0, 0);
}

.primary-button {
    -fx-background-color: #0078d4;
    -fx-text-fill: white;
    -fx-border-color: #0078d4;
}

.primary-button:hover {
    -fx-background-color: #106ebe;
    -fx-border-color: #106ebe;
}

.primary-button:pressed {
    -fx-background-color: #005a9e;
    -fx-border-color: #005a9e;
}

.secondary-button {
    -fx-background-color: #6c757d;
    -fx-text-fill: white;
    -fx-border-color: #6c757d;
}

.secondary-button:hover {
    -fx-background-color: #5a6268;
    -fx-border-color: #5a6268;
}

.danger-button {
    -fx-background-color: #dc3545;
    -fx-text-fill: white;
    -fx-border-color: #dc3545;
}

.danger-button:hover {
    -fx-background-color: #c82333;
    -fx-border-color: #c82333;
}

/* 文本框样式 */
.text-field, .password-field {
    -fx-background-color: #ffffff;
    -fx-border-color: #d1d5db;
    -fx-border-width: 1;
    -fx-border-radius: 6;
    -fx-background-radius: 6;
    -fx-padding: 8 12;
}

.text-field:focused, .password-field:focused {
    -fx-border-color: #0078d4;
    -fx-effect: dropshadow(gaussian, rgba(0,120,212,0.25), 4, 0, 0, 0);
}

/* 下拉框样式 */
.combo-box {
    -fx-background-color: #ffffff;
    -fx-border-color: #d1d5db;
    -fx-border-width: 1;
    -fx-border-radius: 6;
    -fx-background-radius: 6;
}

.combo-box:focused {
    -fx-border-color: #0078d4;
}

.combo-box .arrow-button {
    -fx-background-color: transparent;
}

.combo-box .arrow {
    -fx-background-color: #6b7280;
}

/* 标签页样式 */
.tab-pane {
    -fx-tab-min-height: 40;
}

.tab-pane .tab-header-area {
    -fx-padding: 0;
}

.tab-pane .tab-header-background {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #e9ecef;
    -fx-border-width: 0 0 1 0;
}

.tab-pane .tab {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-padding: 10 20;
    -fx-background-radius: 0;
}

.tab-pane .tab:selected {
    -fx-background-color: #ffffff;
    -fx-border-color: #0078d4;
    -fx-border-width: 0 0 2 0;
}

.tab-pane .tab:hover {
    -fx-background-color: #f1f3f4;
}

.tab-pane .tab .tab-label {
    -fx-text-fill: #374151;
    -fx-font-weight: 500;
}

.tab-pane .tab:selected .tab-label {
    -fx-text-fill: #0078d4;
}

/* 控制台样式 */
.console-output {
    -fx-background-color: #1e1e1e;
    -fx-text-fill: #d4d4d4;
    -fx-font-family: "Consolas", "Monaco", "Courier New", monospace;
    -fx-font-size: 12px;
    -fx-padding: 10;
    -fx-border-color: #3c3c3c;
    -fx-border-width: 1;
    -fx-border-radius: 6;
    -fx-background-radius: 6;
}

.console-output .scroll-pane {
    -fx-background-color: transparent;
}

.console-output .viewport {
    -fx-background-color: transparent;
}

.command-input-area {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1 0 0 0;
}

.command-prompt {
    -fx-text-fill: #0078d4;
    -fx-font-family: "Consolas", "Monaco", "Courier New", monospace;
    -fx-font-weight: bold;
    -fx-font-size: 14px;
}

.command-input {
    -fx-font-family: "Consolas", "Monaco", "Courier New", monospace;
    -fx-font-size: 12px;
}

/* 文件管理样式 */
.file-tree {
    -fx-background-color: #ffffff;
    -fx-border-color: #e5e7eb;
    -fx-border-width: 1;
    -fx-border-radius: 6;
    -fx-background-radius: 6;
}

.file-tree .tree-cell {
    -fx-padding: 4 8;
}

.file-tree .tree-cell:selected {
    -fx-background-color: #e3f2fd;
    -fx-text-fill: #1976d2;
}

.file-table {
    -fx-background-color: #ffffff;
    -fx-border-color: #e5e7eb;
    -fx-border-width: 1;
    -fx-border-radius: 6;
    -fx-background-radius: 6;
}

.file-table .column-header {
    -fx-background-color: #f9fafb;
    -fx-border-color: #e5e7eb;
    -fx-border-width: 0 0 1 0;
    -fx-padding: 8 12;
    -fx-font-weight: 600;
}

.file-table .table-row-cell {
    -fx-border-color: transparent;
}

.file-table .table-row-cell:selected {
    -fx-background-color: #e3f2fd;
    -fx-text-fill: #1976d2;
}

.file-table .table-row-cell:hover {
    -fx-background-color: #f5f5f5;
}

/* 监控样式 */
.monitoring-grid {
    -fx-padding: 20;
    -fx-background-color: #ffffff;
    -fx-border-color: #e5e7eb;
    -fx-border-width: 1;
    -fx-border-radius: 8;
    -fx-background-radius: 8;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 4, 0, 0, 2);
}

.monitor-label {
    -fx-font-weight: 600;
    -fx-text-fill: #374151;
}

.monitor-value {
    -fx-font-weight: 500;
    -fx-text-fill: #1f2937;
}

.network-value {
    -fx-font-family: "Consolas", "Monaco", "Courier New", monospace;
    -fx-font-weight: 500;
    -fx-text-fill: #059669;
}

.cpu-progress .bar {
    -fx-background-color: linear-gradient(to right, #10b981, #f59e0b, #ef4444);
}

.memory-progress .bar {
    -fx-background-color: linear-gradient(to right, #3b82f6, #8b5cf6);
}

.disk-progress .bar {
    -fx-background-color: linear-gradient(to right, #06b6d4, #f59e0b);
}

.progress-bar {
    -fx-background-radius: 10;
    -fx-background-color: #e5e7eb;
}

.progress-bar .bar {
    -fx-background-radius: 10;
    -fx-padding: 4;
}

/* 状态栏样式 */
.status-bar {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1 0 0 0;
    -fx-padding: 5 10;
}

.status-text {
    -fx-text-fill: #6b7280;
    -fx-font-size: 12px;
}

.connection-info {
    -fx-text-fill: #059669;
    -fx-font-weight: 500;
    -fx-font-size: 12px;
}

.time-label {
    -fx-text-fill: #6b7280;
    -fx-font-family: "Consolas", "Monaco", "Courier New", monospace;
    -fx-font-size: 11px;
}

.status-label {
    -fx-text-fill: #6b7280;
    -fx-font-size: 12px;
}

/* 表单样式 */
.form-grid {
    -fx-padding: 20;
    -fx-background-color: #ffffff;
    -fx-border-color: #e5e7eb;
    -fx-border-width: 1;
    -fx-border-radius: 8;
    -fx-background-radius: 8;
}

.section-title {
    -fx-font-size: 18px;
    -fx-font-weight: 600;
    -fx-text-fill: #1f2937;
}

.section-subtitle {
    -fx-font-size: 14px;
    -fx-font-weight: 600;
    -fx-text-fill: #374151;
}

/* 复选框和单选按钮样式 */
.check-box, .radio-button {
    -fx-text-fill: #374151;
}

.check-box .box, .radio-button .radio {
    -fx-background-color: #ffffff;
    -fx-border-color: #d1d5db;
    -fx-border-width: 1;
}

.check-box:selected .mark, .radio-button:selected .dot {
    -fx-background-color: #0078d4;
}

/* 滑块样式 */
.slider .track {
    -fx-background-color: #e5e7eb;
    -fx-background-radius: 10;
}

.slider .thumb {
    -fx-background-color: #0078d4;
    -fx-background-radius: 10;
}

/* 分割面板样式 */
.split-pane {
    -fx-background-color: #ffffff;
}

.split-pane .split-pane-divider {
    -fx-background-color: #e5e7eb;
    -fx-padding: 0 1 0 1;
}

/* 按钮栏样式 */
.button-bar {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1 0 0 0;
}

/* 滚动条样式 */
.scroll-bar {
    -fx-background-color: transparent;
}

.scroll-bar .track {
    -fx-background-color: #f3f4f6;
    -fx-background-radius: 6;
}

.scroll-bar .thumb {
    -fx-background-color: #d1d5db;
    -fx-background-radius: 6;
}

.scroll-bar .thumb:hover {
    -fx-background-color: #9ca3af;
}

.scroll-bar .thumb:pressed {
    -fx-background-color: #6b7280;
}

/* 工具提示样式 */
.tooltip {
    -fx-background-color: #1f2937;
    -fx-text-fill: #ffffff;
    -fx-background-radius: 6;
    -fx-padding: 6 10;
    -fx-font-size: 12px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 4, 0, 0, 2);
}

/* 对话框样式 */
.dialog-pane {
    -fx-background-color: #ffffff;
    -fx-border-color: #e5e7eb;
    -fx-border-width: 1;
    -fx-border-radius: 8;
    -fx-background-radius: 8;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 10, 0, 0, 4);
}

.dialog-pane .header-panel {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #e9ecef;
    -fx-border-width: 0 0 1 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .root {
        -fx-font-size: 12px;
    }
    
    .section-title {
        -fx-font-size: 16px;
    }
    
    .section-subtitle {
        -fx-font-size: 13px;
    }
}
